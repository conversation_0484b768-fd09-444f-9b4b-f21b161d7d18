# Template for Docker Storybook operations
parameters:
  - name: imageRepository
    default: 'rs-ui-react-components-storybook'
  - name: dockerfilePath
    default: '$(Build.SourcesDirectory)/Dockerfile'
  - name: tag
    default: '$(Build.BuildId)'
  - name: testPort
    default: '8080'

steps:
  # Build Docker image
  - task: Docker@2
    displayName: 'Build Storybook Docker image'
    inputs:
      command: 'build'
      repository: ${{ parameters.imageRepository }}
      dockerfile: ${{ parameters.dockerfilePath }}
      tags: |
        ${{ parameters.tag }}
        latest
      arguments: '--no-cache'

  # Test the Docker image
  - script: |
      # Start container in background
      docker run -d --name storybook-test-$(Build.BuildId) -p ${{ parameters.testPort }}:8080 ${{ parameters.imageRepository }}:${{ parameters.tag }}
      
      # Wait for container to start
      echo "Waiting for Storybook to start..."
      for i in {1..30}; do
        if curl -f http://localhost:${{ parameters.testPort }} > /dev/null 2>&1; then
          echo "Storybook is running!"
          break
        fi
        echo "Attempt $i/30: Waiting for Storybook..."
        sleep 2
      done
      
      # Final test
      if ! curl -f http://localhost:${{ parameters.testPort }}; then
        echo "Storybook failed to start properly"
        docker logs storybook-test-$(Build.BuildId)
        exit 1
      fi
      
      echo "Storybook is accessible at http://localhost:${{ parameters.testPort }}"
      
      # Stop and remove test container
      docker stop storybook-test-$(Build.BuildId)
      docker rm storybook-test-$(Build.BuildId)
    displayName: 'Test Docker image'

  # Display image information
  - script: |
      echo "Docker image built successfully:"
      echo "Repository: ${{ parameters.imageRepository }}"
      echo "Tag: ${{ parameters.tag }}"
      echo "Size:"
      docker images ${{ parameters.imageRepository }}:${{ parameters.tag }} --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"
    displayName: 'Display image information'
