# templates/publish.yaml
parameters:
  artifactName: 'rs-ui-react-components'
  updateChangeset: false

steps:
- task: npmAuthenticate@0
  displayName: 'Authenticate to Azure Artifacts'
  inputs:
    workingFile: '.npmrc'
- script: |
    npm publish
  displayName: 'Publish to Azure Artifacts'

- task: PublishBuildArtifacts@1
  displayName: 'Publish component library'
  inputs:
    PathtoPublish: 'dist'
    ArtifactName: 'component-library'
    publishLocation: 'Container'

