import { RiErrorWarningLine } from "@remixicon/react";
import { useState } from "react";
import LabHeaderMenu from "../../components/lab-header-menu/LabHeaderMenu";
import LabTabs from "../../components/lab-tabs/LabTabs";
import RsdsButton from "../../components/rsds-button/RsdsButton";
import SearchPatient from "../search-patient/SearchPatient";

/**
 * Primary UI component for user interaction
 */
export const ShowVe = () => {
	const sampleTabs = [
		{ id: "show-ve", label: "Visa vårdenheter" },
		{ id: "search-patient", label: "Sök patient" },
	];
	const args = {
		tabs: sampleTabs,
		activeTab: "show-ve",
	};

	const [activeTab, setActiveTab] = useState(args.activeTab);
	return (
		<div className="w-full">
			<LabHeaderMenu
				title="Labsvar i Skåne"
				description="Du är inloggad som <PERSON>, VoB Internmedicinsk vård Ystad "
			/>
			<nav className="flex px-10 justify-between">
				<div>Patient: 19930303-2388 <PERSON><PERSON> (Kvinna, 32 år)</div>
				<div className="flex">
					<RiErrorWarningLine className="fill-cp-red" />
					<span>Alla tillgängliga uppgifter visas inte.</span>
				</div>
				<LabTabs {...args} activeTab={activeTab} onTabChange={setActiveTab} />
			</nav>
			<main className="pl-[142px] border-b p-9 flex flex-col gap-y-3">
				<div className="">
					{activeTab === sampleTabs[0].id && (
						<div>
							<div className="font-bold">Din vårdgivare Region Skåne (3)</div>
							<div className="grid grid-cols-2 gap-x-6">
								<div className="bg-red-50">
									<table className="table-auto">
										<thead>
											<tr>
												<th>Song</th> <th>Artist</th> <th>Year</th>
											</tr>
										</thead>
										<tbody>
											<tr>
												<td>The Sliding Mr. Bones (Next Stop, Pottersville)</td>
												<td>Malcolm Lockyer</td> <td>1961</td>
											</tr>
											<tr>
												<td>Witchy Woman</td> <td>The Eagles</td> <td>1972</td>
											</tr>
											<tr>
												<td>Shining Star</td> <td>Earth, Wind, and Fire</td>
												<td>1975</td>
											</tr>
										</tbody>
									</table>
								</div>
								<div className="flex flex-col gap-y-6">
									<div className="bg-gray-300 p-6 rounded-lg">
										<h5>Registrera samtycke</h5>
										<p>
											Välj de enheter i listan som du vill registrera samtycke
											för.{" "}
											<span className="underline">Läs mer om samtycke.</span>
										</p>
									</div>
									<div className="bg-gray-300 p-6 rounded-lg">
										<h5 className="mb-3">Nödöppning</h5>
										<div className="mb-9">
											<p>
												Du kan nödöppna patientens journal om det är en
												nödsituation och patienten inte kan ge samtycke.
											</p>
											<div>Du kan se:</div>
											<ol className="list-disc list-inside">
												<li>Sammanhållen vårddokumentation</li>
												<li>Spärrade enheter inom Region Skåne</li>
											</ol>
										</div>
										<div className="flex items-center justify-end gap-x-3">
											<span>All läsning loggas. </span>
											<form action="">
												<RsdsButton variant="default" form="square">
													Nödöppna
												</RsdsButton>
											</form>
										</div>
									</div>
								</div>
							</div>
						</div>
					)}
					{activeTab === sampleTabs[1].id && <SearchPatient />}
				</div>
			</main>
		</div>
	);
};

export default ShowVe;
