# version-check.yaml
steps:
- task: npmAuthenticate@0
  displayName: 'Authenticate to Azure Artifacts'
  inputs:
    workingFile: '.npmrc'

- script: |
    BEFORE_VERSION=$(npm view rs-ui-react-components version)
    echo "Version before update: $BEFORE_VERSION"
    echo "##vso[task.setvariable variable=BEFORE_VERSION]$BEFORE_VERSION"
  displayName: 'Get Version Before Update'

- script: |
    AFTER_VERSION=$(node -pe "require('./package.json').version")
    echo "Version after update: $AFTER_VERSION"
    echo "##vso[task.setvariable variable=AFTER_VERSION]$AFTER_VERSION"
  displayName: 'Get Version After Update'

- script: |
    BEFORE="$BEFORE_VERSION"
    AFTER="$AFTER_VERSION"

    if [[ "$BEFORE" == "$AFTER" ]]; then
      echo "Error: Package version was not updated!"
      echo "version still $BEFORE == $AFTER"
      echo "update package.json!"
      exit 1
    else
      echo "Package version updated from $BEFORE to $AFTER."
    fi
  displayName: 'Verify Version Update'