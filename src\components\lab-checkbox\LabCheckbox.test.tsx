import { fireEvent, render, screen } from "@testing-library/react";
import { describe, expect, it, vi } from "vitest";
import LabCheckbox from "./LabCheckbox";

describe("LabCheckbox", () => {
	it("renders with label", () => {
		render(<LabCheckbox label="Test Label" />);
		expect(screen.getByText("Test Label")).toBeInTheDocument();
	});

	it("renders with description when provided", () => {
		render(<LabCheckbox label="Test Label" description="Test Description" />);
		expect(screen.getByText("Test Label")).toBeInTheDocument();
		expect(screen.getByText("Test Description")).toBeInTheDocument();
	});

	it("applies bold font to label when description is provided", () => {
		render(<LabCheckbox label="Test Label" description="Test Description" />);
		const label = screen.getByText("Test Label");
		expect(label).toHaveClass("font-bold");
	});

	it("applies normal font to label when no description is provided", () => {
		render(<LabCheckbox label="Test Label" />);
		const label = screen.getByText("Test Label");
		expect(label).toHaveClass("font-normal");
	});

	it("renders unchecked by default", () => {
		render(<LabCheckbox label="Test Label" />);
		const checkbox = screen.getByRole("checkbox");
		expect(checkbox).not.toBeChecked();
	});

	it("renders checked when checked prop is true", () => {
		render(<LabCheckbox label="Test Label" checked={true} />);
		const checkbox = screen.getByRole("checkbox");
		expect(checkbox).toBeChecked();
	});

	it("calls onChange when clicked", () => {
		const handleChange = vi.fn();
		render(<LabCheckbox label="Test Label" onChange={handleChange} />);

		const checkbox = screen.getByRole("checkbox");
		fireEvent.click(checkbox);

		expect(handleChange).toHaveBeenCalledWith(true);
	});

	it("calls onChange with false when checked checkbox is clicked", () => {
		const handleChange = vi.fn();
		render(
			<LabCheckbox label="Test Label" onChange={handleChange} checked={true} />,
		);

		const checkbox = screen.getByRole("checkbox");
		fireEvent.click(checkbox);

		expect(handleChange).toHaveBeenCalledWith(false);
	});

	it("can be clicked via label", () => {
		const handleChange = vi.fn();
		render(<LabCheckbox label="Test Label" onChange={handleChange} />);

		const label = screen.getByText("Test Label");
		fireEvent.click(label);

		expect(handleChange).toHaveBeenCalledWith(true);
	});

	it("applies custom className", () => {
		render(<LabCheckbox label="Test Label" className="custom-class" />);
		const mainContainer = screen
			.getByRole("checkbox")
			.closest("div").parentElement;
		expect(mainContainer).toHaveClass("custom-class");
	});

	it("forwards other props to input", () => {
		render(<LabCheckbox label="Test Label" data-testid="custom-checkbox" />);
		const checkbox = screen.getByTestId("custom-checkbox");
		expect(checkbox).toBeInTheDocument();
	});

	it("generates unique id when not provided", () => {
		const { unmount: unmount1 } = render(<LabCheckbox label="Test Label 1" />);
		const checkbox1 = screen.getByRole("checkbox");
		const id1 = checkbox1.id;
		unmount1();

		const { unmount: unmount2 } = render(<LabCheckbox label="Test Label 2" />);
		const checkbox2 = screen.getByRole("checkbox");
		const id2 = checkbox2.id;
		unmount2();

		expect(id1).not.toBe(id2);
	});

	it("uses provided id", () => {
		// biome-ignore lint/correctness/useUniqueElementIds: Only used for testing
		render(<LabCheckbox label="Test Label" id="custom-id" />);
		const checkbox = screen.getByRole("checkbox");
		expect(checkbox.id).toBe("custom-id");
	});
});
