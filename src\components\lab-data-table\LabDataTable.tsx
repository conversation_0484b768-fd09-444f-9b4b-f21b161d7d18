import { RiArrowDownSLine } from "@remixicon/react";
import "../../styles/public/tailwind.css";
import RsdsLogo from "../rsds-logo/RsdsLogo";

interface PatientInfo {
	id: string;
	name: string;
	gender: string;
	age: number;
}

interface TestRecord {
	date: string;
	orderedBy: string;
	department?: string;
	question?: string;
	analysisGroup: string;
	result: string;
	resultStatus: string;
}

export interface LabDataTableProps {
	patientInfo: PatientInfo;
	records: TestRecord[];
	totalRecords: number;
	shownRecords: number;
	onShowMore?: () => void;
	onShowAll?: () => void;
	onChangePatient?: () => void;
}

export const LabDataTable = ({
	patientInfo,
	records,
	totalRecords,
	shownRecords,
	onShowMore,
	onShowAll,
	onChangePatient,
}: LabDataTableProps) => {
	return (
		<div className="w-full max-w-[1440px] mx-auto bg-white font-sans">
			{/* Header */}
			<header className="border-b border-cp-grey--700 bg-white">
				<div className="flex items-center gap-6 px-12 py-4">
					<RsdsLogo />
					<div className="flex flex-col">
						<h1 className="text-base font-bold text-ct-foreground">
							RS Labbdata
						</h1>
						<p className="text-base text-ct-foreground">
							Tillgängliga provresultat från äldre journalsystem
						</p>
					</div>
				</div>
			</header>

			{/* Patient Info */}
			<div className="px-6 md:px-12 py-8">
				<div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-8">
					<div className="flex flex-col md:flex-row md:items-center gap-4">
						<h2 className="text-2xl md:text-3xl font-bold text-ct-foreground">
							{patientInfo.id}
						</h2>
						<span className="text-2xl md:text-3xl text-ct-foreground">
							{patientInfo.name} ({patientInfo.gender}, {patientInfo.age} år)
						</span>
					</div>
					<button
						type="button"
						onClick={onChangePatient}
						className="text-ct-link underline text-base hover:text-cp-saphire--bright transition-colors"
					>
						Byt patient
					</button>
				</div>

				{/* Tabs */}
				<div className="border-b border-cp-grey--175 mb-8">
					<div className="flex flex-wrap gap-0">
						<div className="px-3 py-3 border-b-2 border-cp-sea--dark">
							<span className="text-base font-bold text-black">
								Provtagningstillfällen
							</span>
						</div>
						<div className="px-3 py-3">
							<span className="text-base font-bold text-cp-sea--dark">
								Klinisk kemi
							</span>
						</div>
						<div className="px-3 py-3">
							<span className="text-base font-bold text-cp-sea--dark">
								Mikrobiologi
							</span>
						</div>
						<div className="px-3 py-3">
							<span className="text-base font-bold text-cp-sea--dark">
								Cytologi och Patologi
							</span>
						</div>
					</div>
				</div>

				{/* Records count */}
				<p className="text-base text-ct-foreground mb-4">
					Visar {shownRecords} av {totalRecords} provtagningar
				</p>

				{/* Table */}
				<div className="overflow-x-auto">
					{/* Table Header */}
					<div className="hidden md:grid md:grid-cols-5 gap-4 border-b-2 border-cp-grey--700 py-3">
						<div className="px-4 text-base font-bold text-ct-foreground">
							Provtagningstid
						</div>
						<div className="px-4 text-base font-bold text-ct-foreground">
							Beställt av
						</div>
						<div className="px-4 text-base font-bold text-ct-foreground">
							Frågeställning
						</div>
						<div className="px-4 text-base font-bold text-ct-foreground">
							Analysgrupp
						</div>
						<div className="px-4 text-base font-bold text-ct-foreground">
							Svar
						</div>
					</div>

					{/* Table Rows */}
					<div className="divide-y divide-cp-grey--700">
						{records.map((record) => (
							<div key={record.date} className="py-4">
								{/* Mobile View */}
								<div className="md:hidden space-y-3">
									<div className="flex justify-between items-start">
										<div>
											<div className="text-sm font-semibold text-ct-foreground mb-1">
												{record.date}
											</div>
											<div className="text-sm text-ct-foreground">
												{record.orderedBy}
												{record.department && (
													<div className="text-sm text-ct-foreground">
														{record.department}
													</div>
												)}
											</div>
										</div>
										<RiArrowDownSLine />
									</div>
									{record.question && (
										<div className="text-sm text-ct-foreground">
											<span className="font-semibold">Frågeställning: </span>
											{record.question}
										</div>
									)}
									<div className="text-sm text-ct-foreground">
										<span className="font-semibold">Analysgrupp: </span>
										{record.analysisGroup}
									</div>
									<div className="flex justify-between items-center">
										<div className="text-sm text-ct-foreground">
											<span className="text-base">{record.result} </span>
											<span className="text-xs">{record.resultStatus}</span>
										</div>
									</div>
								</div>

								{/* Desktop View */}
								<div className="hidden md:grid md:grid-cols-5 gap-4 items-center">
									<div className="px-4 py-3 text-base text-ct-foreground">
										{record.date}
									</div>
									<div className="px-4 py-3 text-base text-ct-foreground">
										{record.orderedBy}
										{record.department && (
											<div className="text-base text-ct-foreground">
												{record.department}
											</div>
										)}
									</div>
									<div className="px-4 py-3 text-base text-ct-foreground">
										{record.question || ""}
									</div>
									<div className="px-4 py-3 text-base text-ct-foreground">
										{record.analysisGroup}
									</div>
									<div className="px-4 py-3 flex items-center justify-between">
										<div className="text-ct-foreground">
											<span className="text-base">{record.result} </span>
											<span className="text-xs">{record.resultStatus}</span>
										</div>
										<RiArrowDownSLine />
									</div>
								</div>
							</div>
						))}
					</div>
				</div>

				{/* Action Buttons */}
				<div className="flex flex-col sm:flex-row gap-4 items-center justify-center mt-8">
					<button
						type="button"
						onClick={onShowMore}
						className="inline-flex px-6 py-3 justify-center items-center gap-4 rounded-full border-2 border-ct-border bg-white text-base font-bold text-ct-foreground hover:bg-ct-button-bg--hover transition-colors"
					>
						Visa fler
					</button>
					<button
						type="button"
						onClick={onShowAll}
						className="inline-flex px-6 py-3 justify-center items-center gap-4 rounded-full bg-transparent text-base font-bold text-cp-sea--dark underline hover:text-cp-sea transition-colors"
					>
						Visa alla
					</button>
				</div>
			</div>
		</div>
	);
};

export default LabDataTable;
