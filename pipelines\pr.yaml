# This triggers the pipeline on Pull Requests targeting main
name: rs-ui-react-components PR Pipeline

pr:
  branches:
    include:
      - main

pool:
  vmImage: 'ubuntu-latest'

variables:
  nodeVersion: '22.x'

stages:
  - stage: Build
    displayName: Build and Test
    jobs:
      - job: ChangeDetection
        displayName: Change Detection
        timeoutInMinutes: 5
        steps:
          - bash: |
              CHANGED_FILES=$(git diff --name-only $(System.PullRequest.TargetBranch)..HEAD -- src/)
              if [ -n "$CHANGED_FILES" ]; then
                echo "##vso[task.setvariable variable=SHOULD_RUN_VERSION_CHECK;isOutput=true]true"
                echo "Förändringar hittades i 'src/'. Bygg- och teststeg kommer att köras."
              else
                echo "Inga förändringar hittades i 'src/'. Kontroll av versionsnummer kommer hoppas över."
              fi
            displayName: 'Check for changes in src/'
            name: changeCheck

      - job: VersionCheck
        displayName: Validate Version Bump
        dependsOn: ChangeDetection
        condition: eq(dependencies.changeCheck.outputs['SHOULD_RUN_VERSION_CHECK'], 'true')
        steps:
          - template: templates/version-check.yaml
          
      - job: BuildAndTest
        displayName: Build, Test
        timeoutInMinutes: 10
        steps:
          - template: templates/setup.yaml
            parameters:
              nodeVersion: $(nodeVersion)
              
          - template: templates/test.yaml

          - template: templates/build.yaml
            parameters:
              buildArguments: ''
  - stage: Scan
    displayName: Security Scanning
    dependsOn: []
    jobs:
      - job: SecurityScan
        displayName: Security Scanning
        timeoutInMinutes: 15
        steps:
        - task: AdvancedSecurity-Codeql-Init@1
          inputs:
            languages: 'javascript'
            querysuite: 'security-and-quality'
            enableAutomaticCodeQLInstall: true
            buildtype: 'none'
          displayName: 'Initialize CodeQL'
      
        - task: AdvancedSecurity-Codeql-Analyze@1
          displayName: 'Run CodeQL Analysis'
      
        - task: AdvancedSecurity-Dependency-Scanning@1
          displayName: 'Run Dependency Scanning'

