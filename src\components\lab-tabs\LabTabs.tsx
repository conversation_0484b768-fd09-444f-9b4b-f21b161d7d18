import type { ReactNode } from "react";
import "../../styles/public/tailwind.css";

export interface TabItem {
	/**
	 * Unique identifier for the tab
	 */
	id: string;
	/**
	 * Tab label text
	 */
	label: string;
	/**
	 * Tab content (optional)
	 */
	content?: ReactNode;
}

export interface LabTabsProps {
	/**
	 * Array of tab items
	 */
	tabs: TabItem[];
	/**
	 * Currently active tab ID
	 */
	activeTab: string;
	/**
	 * Callback fired when a tab is clicked
	 */
	onTabChange: (tabId: string) => void;
	/**
	 * Additional CSS classes for the container
	 */
	className?: string;
}

/**
 * Tab component for navigating between different content sections
 */
export const LabTabs = ({
	tabs,
	activeTab,
	onTabChange,
	className = "",
}: LabTabsProps) => {
	return (
		<div className={className}>
			<div className="flex items-center border-b border-cp-grey--175">
				{tabs.map((tab) => {
					const isActive = tab.id === activeTab;

					return (
						<button
							key={tab.id}
							type="button"
							onClick={() => onTabChange(tab.id)}
							className={`
								flex justify-center items-center gap-3 px-3 py-3
								font-sans text-base font-bold leading-6
								transition-colors duration-200
								border-b-4
								${
									isActive
										? "text-ct-foreground border-cp-sea--dark"
										: "text-cp-sea--dark border-transparent hover:text-ct-foreground"
								}
							`}
						>
							{tab.label}
						</button>
					);
				})}
			</div>
		</div>
	);
};

export default LabTabs;
