import { fireEvent, render, screen } from "@testing-library/react";
import { describe, expect, it, vi } from "vitest";
import LabSearchInput from "./LabSearchInput";

describe("LabSearchInput", () => {
	it("renders with placeholder", () => {
		render(<LabSearchInput placeholder="Search for something..." />);
		expect(
			screen.getByPlaceholderText("Search for something..."),
		).toBeInTheDocument();
	});

	it("renders with initial value", () => {
		render(<LabSearchInput value="test value" />);
		const input = screen.getByDisplayValue("test value");
		expect(input).toBeInTheDocument();
	});

	it("calls onChange when typing", () => {
		const handleChange = vi.fn();
		render(<LabSearchInput onChange={handleChange} />);

		const input = screen.getByRole("textbox");
		fireEvent.change(input, { target: { value: "test" } });

		expect(handleChange).toHaveBeenCalledWith("test");
	});

	it("calls onSearch when search button is clicked", () => {
		const handleSearch = vi.fn();
		render(<LabSearchInput value="search term" onSearch={handleSearch} />);

		const searchButton = screen.getByRole("button", { name: "Sök" });
		fireEvent.click(searchButton);

		expect(handleSearch).toHaveBeenCalledWith("search term");
	});

	it("calls onSearch when Enter key is pressed", () => {
		const handleSearch = vi.fn();
		render(<LabSearchInput value="search term" onSearch={handleSearch} />);

		const input = screen.getByRole("textbox");
		fireEvent.keyDown(input, { key: "Enter", code: "Enter" });

		expect(handleSearch).toHaveBeenCalledWith("search term");
	});

	it("does not call onSearch when other keys are pressed", () => {
		const handleSearch = vi.fn();
		render(<LabSearchInput value="search term" onSearch={handleSearch} />);

		const input = screen.getByRole("textbox");
		fireEvent.keyDown(input, { key: "Tab", code: "Tab" });
		fireEvent.keyDown(input, { key: "Escape", code: "Escape" });

		expect(handleSearch).not.toHaveBeenCalled();
	});

	it("renders search icon", () => {
		render(<LabSearchInput />);
		const searchButton = screen.getByRole("button", { name: "Sök" });
		expect(searchButton).toBeInTheDocument();
		expect(searchButton.querySelector("svg")).toBeInTheDocument();
	});

	it("applies custom className", () => {
		render(<LabSearchInput className="custom-search" />);
		const mainContainer = screen
			.getByRole("textbox")
			.closest("div").parentElement;
		expect(mainContainer).toHaveClass("custom-search");
	});

	it("forwards input props", () => {
		render(<LabSearchInput data-testid="search-input" maxLength={100} />);
		const input = screen.getByTestId("search-input");
		expect(input).toBeInTheDocument();
		expect(input).toHaveAttribute("maxLength", "100");
	});

	it("generates unique id when not provided", () => {
		const { unmount: unmount1 } = render(<LabSearchInput />);
		const input1 = screen.getByRole("textbox");
		const id1 = input1.id;
		unmount1();

		const { unmount: unmount2 } = render(<LabSearchInput />);
		const input2 = screen.getByRole("textbox");
		const id2 = input2.id;
		unmount2();

		expect(id1).not.toBe(id2);
	});

	it("uses provided id", () => {
		// biome-ignore lint/correctness/useUniqueElementIds: Only used for testing
		render(<LabSearchInput id="custom-search-id" />);
		const input = screen.getByRole("textbox");
		expect(input.id).toBe("custom-search-id");
	});

	it("handles disabled state", () => {
		render(<LabSearchInput disabled />);
		const input = screen.getByRole("textbox");
		expect(input).toBeDisabled();
	});

	it("does not trigger onSearch when disabled and Enter is pressed", () => {
		const handleSearch = vi.fn();
		render(<LabSearchInput value="test" onSearch={handleSearch} disabled />);

		const input = screen.getByRole("textbox");
		fireEvent.keyDown(input, { key: "Enter", code: "Enter" });

		expect(handleSearch).not.toHaveBeenCalled();
	});

	it("handles empty value correctly", () => {
		const handleSearch = vi.fn();
		render(<LabSearchInput value="" onSearch={handleSearch} />);

		const searchButton = screen.getByRole("button", { name: "Sök" });
		fireEvent.click(searchButton);

		expect(handleSearch).toHaveBeenCalledWith("");
	});

	it("updates value when controlled", () => {
		const { rerender } = render(<LabSearchInput value="initial" />);
		expect(screen.getByDisplayValue("initial")).toBeInTheDocument();

		rerender(<LabSearchInput value="updated" />);
		expect(screen.getByDisplayValue("updated")).toBeInTheDocument();
	});
});
