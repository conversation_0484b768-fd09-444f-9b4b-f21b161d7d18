
steps:
  - script: |
      pnpm run test
    displayName: 'Run Vitest unit tests'
    
  - script: |
      pnpm test:coverage
    displayName: 'Run tests with coverage'


  - task: PublishTestResults@2
    displayName: 'Publish test results'
    inputs:
      testResultsFormat: 'JUnit'
      testResultsFiles: '**/junit.xml'
      mergeTestResults: true
      testRunTitle: 'Component Tests'
    condition: succeededOrFailed()

  - task: PublishCodeCoverageResults@2
    displayName: 'Publish code coverage'
    inputs:
      codeCoverageTool: 'Cobertura'
      summaryFileLocation: '$(System.DefaultWorkingDirectory)/coverage/cobertura-coverage.xml'
      reportDirectory: '$(System.DefaultWorkingDirectory)/coverage'
    condition: succeededOrFailed()
