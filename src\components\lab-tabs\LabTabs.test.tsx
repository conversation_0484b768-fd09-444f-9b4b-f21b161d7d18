import { fireEvent, render, screen } from "@testing-library/react";
import { describe, expect, it, vi } from "vitest";
import LabTabs from "./LabTabs";

const sampleTabs = [
	{ id: "tab1", label: "Tab 1" },
	{ id: "tab2", label: "Tab 2" },
];

describe("LabTabs", () => {
	it("renders all tabs", () => {
		const handleTabChange = vi.fn();
		render(
			<LabTabs
				tabs={sampleTabs}
				activeTab="tab1"
				onTabChange={handleTabChange}
			/>,
		);

		expect(screen.getByText("Tab 1")).toBeInTheDocument();
		expect(screen.getByText("Tab 2")).toBeInTheDocument();
	});

	it("highlights the active tab", () => {
		const handleTabChange = vi.fn();
		render(
			<LabTabs
				tabs={sampleTabs}
				activeTab="tab1"
				onTabChange={handleTabChange}
			/>,
		);

		const activeTab = screen.getByText("Tab 1");
		const inactiveTab = screen.getByText("Tab 2");

		expect(activeTab).toHaveClass("border-cp-sea--dark");
		expect(inactiveTab).toHaveClass("border-transparent");
	});

	it("calls onTabChange when a tab is clicked", () => {
		const handleTabChange = vi.fn();
		render(
			<LabTabs
				tabs={sampleTabs}
				activeTab="tab1"
				onTabChange={handleTabChange}
			/>,
		);

		const tab2 = screen.getByText("Tab 2");
		fireEvent.click(tab2);

		expect(handleTabChange).toHaveBeenCalledWith("tab2");
	});

	it("calls onTabChange when active tab is clicked", () => {
		const handleTabChange = vi.fn();
		render(
			<LabTabs
				tabs={sampleTabs}
				activeTab="tab1"
				onTabChange={handleTabChange}
			/>,
		);

		const activeTab = screen.getByText("Tab 1");
		fireEvent.click(activeTab);

		expect(handleTabChange).toHaveBeenCalledWith("tab1");
	});

	it("applies custom className", () => {
		const handleTabChange = vi.fn();
		render(
			<LabTabs
				tabs={sampleTabs}
				activeTab="tab1"
				onTabChange={handleTabChange}
				className="custom-tabs"
			/>,
		);

		const container = screen.getByText("Tab 1").closest("div").parentElement;
		expect(container).toHaveClass("custom-tabs");
	});

	it("renders with different active tab", () => {
		const handleTabChange = vi.fn();
		render(
			<LabTabs
				tabs={sampleTabs}
				activeTab="tab2"
				onTabChange={handleTabChange}
			/>,
		);

		const tab1 = screen.getByText("Tab 1");
		const tab2 = screen.getByText("Tab 2");

		expect(tab1).toHaveClass("border-transparent");
		expect(tab2).toHaveClass("border-cp-sea--dark");
	});

	it("handles empty tabs array", () => {
		const handleTabChange = vi.fn();
		const { container } = render(
			<LabTabs tabs={[]} activeTab="" onTabChange={handleTabChange} />,
		);

		expect(container).toBeInTheDocument();
		expect(container.querySelector("button")).toBeNull();
	});

	it("renders tabs as buttons", () => {
		const handleTabChange = vi.fn();
		render(
			<LabTabs
				tabs={sampleTabs}
				activeTab="tab1"
				onTabChange={handleTabChange}
			/>,
		);

		const tab1Button = screen.getByRole("button", { name: "Tab 1" });
		const tab2Button = screen.getByRole("button", { name: "Tab 2" });

		expect(tab1Button).toBeInTheDocument();
		expect(tab2Button).toBeInTheDocument();
	});

	it("supports many tabs", () => {
		const manyTabs = [
			{ id: "tab1", label: "First Tab" },
			{ id: "tab2", label: "Second Tab" },
			{ id: "tab3", label: "Third Tab" },
			{ id: "tab4", label: "Fourth Tab" },
		];

		const handleTabChange = vi.fn();
		render(
			<LabTabs
				tabs={manyTabs}
				activeTab="tab3"
				onTabChange={handleTabChange}
			/>,
		);

		expect(screen.getByText("First Tab")).toBeInTheDocument();
		expect(screen.getByText("Second Tab")).toBeInTheDocument();
		expect(screen.getByText("Third Tab")).toBeInTheDocument();
		expect(screen.getByText("Fourth Tab")).toBeInTheDocument();

		expect(screen.getByText("Third Tab")).toHaveClass("border-cp-sea--dark");
	});
});
