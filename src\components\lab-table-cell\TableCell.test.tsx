import { render, screen } from "@testing-library/react";
import { describe, expect, it } from "vitest";
import { LabTableCell } from "./LabTableCell";

describe("TableCell", () => {
	it("renders with default props", () => {
		render(
			<table>
				<tbody>
					<tr>
						<LabTableCell>Test Content</LabTableCell>
					</tr>
				</tbody>
			</table>,
		);

		const cell = screen.getByRole("cell");
		expect(cell).toBeInTheDocument();
		expect(cell).toHaveTextContent("Test Content");
	});

	it("renders as td by default", () => {
		render(
			<table>
				<tbody>
					<tr>
						<LabTableCell>Test Content</LabTableCell>
					</tr>
				</tbody>
			</table>,
		);

		const cell = screen.getByRole("cell");
		expect(cell.tagName).toBe("TD");
	});

	it("renders as th when type is th", () => {
		render(
			<table>
				<thead>
					<tr>
						<LabTableCell type="th">Test Header</LabTableCell>
					</tr>
				</thead>
			</table>,
		);

		const cell = screen.getByRole("columnheader");
		expect(cell.tagName).toBe("TH");
	});

	it("renders with normal variant by default", () => {
		render(
			<table>
				<tbody>
					<tr>
						<LabTableCell>Test Content</LabTableCell>
					</tr>
				</tbody>
			</table>,
		);

		const cell = screen.getByRole("cell");

		expect(cell).toHaveClass(
			"border-b-ct-foreground",
			"text-ct-foreground",
			"font-normal",
		);
	});

	it("renders with elevated variant styling", () => {
		render(
			<table>
				<tbody>
					<tr>
						<LabTableCell variant="elevated">↥ 0,2x10⌃9/L</LabTableCell>
					</tr>
				</tbody>
			</table>,
		);

		const cell = screen.getByRole("cell");
		expect(cell).toHaveClass(
			"border-b-cp-grey--700",
			"text-cp-red",
			"font-bold",
		);
	});

	it("renders with left alignment by default", () => {
		render(
			<table>
				<tbody>
					<tr>
						<LabTableCell>Test Content</LabTableCell>
					</tr>
				</tbody>
			</table>,
		);

		const cell = screen.getByRole("cell");
		expect(cell).toHaveClass("items-start", "text-left");
	});

	it("renders with center alignment", () => {
		render(
			<table>
				<tbody>
					<tr>
						<LabTableCell textAlign="center">Test Content</LabTableCell>
					</tr>
				</tbody>
			</table>,
		);

		const cell = screen.getByRole("cell");
		expect(cell).toHaveClass("items-center", "text-center");
	});

	it("renders with right alignment", () => {
		render(
			<table>
				<tbody>
					<tr>
						<LabTableCell textAlign="right">Test Content</LabTableCell>
					</tr>
				</tbody>
			</table>,
		);

		const cell = screen.getByRole("cell");
		expect(cell).toHaveClass("items-end", "text-right");
	});

	it("applies custom className", () => {
		render(
			<table>
				<tbody>
					<tr>
						<LabTableCell className="custom-class">Test Content</LabTableCell>
					</tr>
				</tbody>
			</table>,
		);

		const cell = screen.getByRole("cell");
		expect(cell).toHaveClass("custom-class");
	});

	it("passes through other HTML attributes", () => {
		render(
			<table>
				<tbody>
					<tr>
						{/** biome-ignore lint/correctness/useUniqueElementIds: only for testing */}
						<LabTableCell data-testid="custom-cell" id="cell-1">
							Test Content
						</LabTableCell>
					</tr>
				</tbody>
			</table>,
		);

		const cell = screen.getByTestId("custom-cell");
		expect(cell).toHaveAttribute("id", "cell-1");
	});

	it("has proper base styling classes", () => {
		render(
			<table>
				<tbody>
					<tr>
						<LabTableCell>Test Content</LabTableCell>
					</tr>
				</tbody>
			</table>,
		);

		const cell = screen.getByRole("cell");
		expect(cell).toHaveClass(
			"flex",
			"w-36",
			"px-4",
			"py-3",
			"flex-col",
			"h-12",
			"border-b",
			"text-base",
			"leading-6",
		);
	});
});
