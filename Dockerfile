# --- Stage 1: Build the Storybook ---
FROM node:22-alpine AS builder

# Install pnpm and set up the working directory
RUN npm install -g pnpm
WORKDIR /app

# Copy and install dependencies
COPY package.json pnpm-lock.yaml ./
RUN pnpm install --frozen-lockfile

# Copy project files
COPY . .

# Build the Storybook static assets
RUN pnpm run build-storybook

# --- Stage 2: Serve with http-server ---
FROM node:22-alpine AS production

# Install http-server globally
RUN npm install -g http-server

# Copy the built Storybook files from the previous stage
COPY --from=builder /app/storybook-static /usr/share/app/storybook-static

# Set the working directory to where the files are located
WORKDIR /usr/share/app/storybook-static

# Expose port 8080 (the default for http-server)
EXPOSE 8080

# Command to start the http-server
CMD ["http-server", "-a", "0.0.0.0"]