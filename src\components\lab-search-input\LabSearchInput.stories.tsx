/** biome-ignore-all lint/suspicious/noArrayIndexKey: only used for testing */
import type { Meta, StoryObj } from "@storybook/react-vite";
import React from "react";
import LabSearchInput from "./LabSearchInput";

const meta: Meta<typeof LabSearchInput> = {
	title: "UI/Lab Search Input",
	component: LabSearchInput,
	parameters: {
		layout: "centered",
	},
	tags: ["autodocs"],
	argTypes: {
		value: {
			control: { type: "text" },
		},
		placeholder: {
			control: { type: "text" },
		},
	},
};

export default meta;
type Story = StoryObj<typeof LabSearchInput>;

export const Default: Story = {
	args: {
		placeholder: "Search...",
		value: "",
		onChange: (value) => console.log("Search value:", value),
		onSearch: (value) => console.log("Searching for:", value),
	},
};

export const WithValue: Story = {
	args: {
		placeholder: "Search...",
		value: "Sök analys eller grupp",
		onChange: (value) => console.log("Search value:", value),
		onSearch: (value) => console.log("Searching for:", value),
	},
};

export const WithPlaceholder: Story = {
	args: {
		placeholder: "Sök analys eller grupp",
		value: "",
		onChange: (value) => console.log("Search value:", value),
		onSearch: (value) => console.log("Searching for:", value),
	},
};

export const Interactive: Story = {
	args: {
		placeholder: "Type to search...",
	},
	render: (args) => {
		const [searchValue, setSearchValue] = React.useState("");
		const [searchResults, setSearchResults] = React.useState<string[]>([]);

		const handleSearch = (value: string) => {
			console.log("Searching for:", value);
			// Simulate search results
			if (value.trim()) {
				setSearchResults([
					`Result 1 for "${value}"`,
					`Result 2 for "${value}"`,
					`Result 3 for "${value}"`,
				]);
			} else {
				setSearchResults([]);
			}
		};

		return (
			<div className="w-96">
				<LabSearchInput
					{...args}
					value={searchValue}
					onChange={setSearchValue}
					onSearch={handleSearch}
				/>
				{searchResults.length > 0 && (
					<div className="mt-4 p-4 bg-ct-background border border-ct-border rounded">
						<h3 className="text-ct-foreground font-sans font-bold mb-2">
							Search Results:
						</h3>
						<ul className="space-y-1">
							{searchResults.map((result, index) => (
								<li
									key={index}
									className="text-ct-foreground font-sans text-sm"
								>
									{result}
								</li>
							))}
						</ul>
					</div>
				)}
			</div>
		);
	},
};

export const Disabled: Story = {
	args: {
		placeholder: "Search disabled...",
		value: "",
		disabled: true,
		onChange: (value) => console.log("Search value:", value),
		onSearch: (value) => console.log("Searching for:", value),
	},
};
