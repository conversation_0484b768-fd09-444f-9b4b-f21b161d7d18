import type { ButtonHTMLAttributes } from "react";

export interface TempButtonProps
	extends ButtonHTMLAttributes<HTMLButtonElement> {
	/**
	 * Button contents
	 */
	children: React.ReactNode;
	/**
	 * Optional click handler
	 */
	onClick?: () => void;
	/**
	 * Button variant
	 */
	variant?: "primary" | "secondary" | "outline";
	/**
	 * Button size
	 */
	size?: "small" | "medium" | "large";
}

/**
 * Primary UI component for user interaction
 */
export const TempButton = ({
	children,
	variant = "primary",
	size = "medium",
	...props
}: TempButtonProps) => {
	const baseClasses = "px-4 rounded font-semibold";

	const variantClasses = {
		primary: "bg-blue-600 text-white hover:bg-blue-700",
		secondary: "bg-gray-600 text-white hover:bg-gray-700",
		outline: "border border-gray-300 text-gray-700 hover:bg-gray-50",
	};

	const sizeClasses = {
		small: "py-1 text-sm",
		medium: "py-2 text-base",
		large: "py-3 text-lg",
	};

	return (
		<button
			type="button"
			className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]}`}
			{...props}
		>
			{children}
		</button>
	);
};

export default TempButton;
