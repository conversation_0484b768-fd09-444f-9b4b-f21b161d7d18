import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";
import { LabTableCell } from "./LabTableCell";

const meta: Meta<typeof LabTableCell> = {
	title: "UI/Lab TableCell",
	component: LabTableCell,
	parameters: {
		layout: "centered",
	},
	tags: ["autodocs"],
	argTypes: {
		variant: {
			control: "select",
			options: ["normal", "elevated"],
		},
		textAlign: {
			control: "select",
			options: ["left", "center", "right"],
		},
		type: {
			control: "select",
			options: ["td", "th"],
		},
		children: {
			control: "text",
		},
	},
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Normal: Story = {
	args: {
		children: "0,3x10⌃9/L",
		variant: "normal",
	},
	render: (args) => (
		<table className="border-collapse">
			<tbody>
				<tr>
					<LabTableCell {...args} />
				</tr>
			</tbody>
		</table>
	),
};

export const Elevated: Story = {
	args: {
		children: "↥ 0,2x10⌃9/L",
		variant: "elevated",
	},
	render: (args) => (
		<table className="border-collapse">
			<tbody>
				<tr>
					<LabTableCell {...args} />
				</tr>
			</tbody>
		</table>
	),
};

export const CenterAligned: Story = {
	args: {
		children: "1,5x10⌃9/L",
		variant: "normal",
		textAlign: "center",
	},
	render: (args) => (
		<table className="border-collapse">
			<tbody>
				<tr>
					<LabTableCell {...args} />
				</tr>
			</tbody>
		</table>
	),
};

export const RightAligned: Story = {
	args: {
		children: "↥ 2,1x10⌃9/L",
		variant: "elevated",
		textAlign: "right",
	},
	render: (args) => (
		<table className="border-collapse">
			<tbody>
				<tr>
					<LabTableCell {...args} />
				</tr>
			</tbody>
		</table>
	),
};

export const MultipleCells: Story = {
	render: () => (
		<table className="border-collapse">
			<tbody>
				<tr>
					<LabTableCell variant="normal">0,3x10⌃9/L</LabTableCell>
					<LabTableCell variant="elevated">↥ 0,2x10⌃9/L</LabTableCell>
					<LabTableCell variant="normal">1,1x10⌃9/L</LabTableCell>
					<LabTableCell variant="elevated">↥ 3,2x10⌃9/L</LabTableCell>
				</tr>
				<tr>
					<LabTableCell variant="elevated">↥ 4,1x10⌃9/L</LabTableCell>
					<LabTableCell variant="normal">0,8x10⌃9/L</LabTableCell>
					<LabTableCell variant="normal">2,3x10⌃9/L</LabTableCell>
					<LabTableCell variant="elevated">↥ 5,7x10⌃9/L</LabTableCell>
				</tr>
			</tbody>
		</table>
	),
};
