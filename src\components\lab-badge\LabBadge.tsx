import type { HTMLAttributes } from "react";
import "../../styles/public/tailwind.css";
import { RiCloseLine } from "@remixicon/react";

export interface LabBadgeProps
	extends Omit<HTMLAttributes<HTMLDivElement>, "onClick"> {
	/**
	 * Badge text content
	 */
	children: React.ReactNode;
	/**
	 * Whether to show the close button
	 */
	dismissible?: boolean;
	/**
	 * Callback fired when the close button is clicked
	 */
	onDismiss?: () => void;
	/**
	 * Badge variant for different styling
	 */
	variant?: "default" | "info" | "success" | "warning" | "error";
	/**
	 * Badge size
	 */
	size?: "small" | "medium" | "large";
	/**
	 * Additional CSS classes
	 */
	className?: string;
}

/**
 * Badge component for displaying labels with optional dismiss functionality
 */
export const LabBadge = ({
	children,
	dismissible = false,
	onDismiss,
	variant = "default",
	size = "medium",
	className = "",
	...props
}: LabBadgeProps) => {
	const baseClasses = "inline-flex items-center gap-3 font-bold font-sans";

	const sizeClasses = {
		small: "h-7 px-2 text-sm leading-5",
		medium: "h-9 px-3 text-base leading-6",
		large: "h-11 px-4 text-lg leading-7",
	};

	const variantClasses = {
		default: "bg-cp-grey--150 text-ct-foreground rounded",
		info: "bg-cp-saphire--bright-alpha-12 text-cp-saphire border border-cp-saphire--bright rounded",
		success:
			"bg-cp-1177-grass-background text-cp-1177-grass-base border border-cp-1177-grass-line rounded",
		warning:
			"bg-cp-1177-sun-background text-cp-1177-sun-base border border-cp-1177-sun-line rounded",
		error: "bg-cp-red--bright text-cp-white rounded",
	};

	const iconSizes = {
		small: 16,
		medium: 20,
		large: 24,
	};

	return (
		<div
			className={`${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${className}`}
			{...props}
		>
			<span className="flex-1">{children}</span>
			{dismissible && (
				<button
					type="button"
					onClick={onDismiss}
					className="flex items-center justify-center w-6 h-6 rounded-sm hover:bg-ct-bg--hover focus:ring-2 focus:ring-ct-focus focus:ring-offset-1 transition-colors"
					aria-label="Remove badge"
				>
					<RiCloseLine size={iconSizes[size]} />
				</button>
			)}
		</div>
	);
};

export default LabBadge;
