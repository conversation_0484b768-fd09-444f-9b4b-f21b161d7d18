{{- if .Values.ingress.enabled -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ include "dev-storybook.fullname" . }}-ingress
  labels:
    {{- include "dev-storybook.labels" . | nindent 4 }}

  annotations:
    traefik.ingress.kubernetes.io/router.middlewares: {{ $.Release.Namespace }}-{{ include "dev-storybook.fullname" . }}-middleware@kubernetescrd
    traefik.ingress.kubernetes.io/router.tls.options: {{ $.Release.Namespace }}-{{ include "dev-storybook.fullname" . }}-tlsoption@kubernetescrd        
  {{- with .Values.ingress.annotations }}
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- with .Values.ingress.className }}
  ingressClassName: {{ . }}
  {{- end }}
  {{- if .Values.ingress.tls }}
  tls:
    {{- range .Values.ingress.tls }}
    - hosts:
        {{- range .hosts }}
        - {{ . | quote }}
        {{- end }}
      secretName: {{ .secretName }}
    {{- end }}
  {{- end }}
  rules:
    {{- range .Values.ingress.hosts }}
    - host: {{ .host | quote }}
      http:
        paths:
          {{- range .paths }}
          - path: {{ .path }}
            {{- with .pathType }}
            pathType: {{ . }}
            {{- end }}
            backend:
              service:
                {{- with .backendServiceName }}
                name: {{ . }}
                {{- end }}
                port:
                  {{- with .backendServicePort }}
                  number: {{ . }}
                  {{- end }}
          {{- end }}
    {{- end }}
{{- end }}
