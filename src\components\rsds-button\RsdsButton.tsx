import type { ButtonHTMLAttributes } from "react";

export interface RsdsButtonProps
	extends ButtonHTMLAttributes<HTMLButtonElement> {
	/**
	 * Button contents
	 */
	children: React.ReactNode;
	/**
	 * Optional click handler
	 */
	onClick?: () => void;
	/**
	 * Button variant
	 */
	variant?: "main" | "default" | "ghost";

	form?: "rounded" | "square";
	disabled?: boolean;
}

/**
 * Primary UI component for user interaction
 */
export const RsdsButton = ({
	children,
	className = "",
	variant = "default",
	form = "rounded",
	...props
}: RsdsButtonProps) => {
	const baseClasses = `p-3 border-2
	w-fit
	font-bold
		disabled:bg-main-button-bg--disabled 
		disabled:text-main-button-fg--disabled
		outline-none

		focus-visible:ring-2 
		focus-visible:ring-offset-2
		focus-visible:ring-focus
		focus-visible:ring-offset-white
		dark:focus-visible:ring-offset-grey--800
		focus:ring-2 
		focus:ring-offset-2
		focus:ring-focus
		focus:ring-offset-white

		dark:focus:ring-offset-grey--800
		`;

	const baseVariantClasses = `text-foreground
		dark:text-white
	`;

	const variantClasses = {
		main: `bg-main-button-bg
		text-main-button-fg
		border-transparent
		
		hover:bg-main-button-bg--hover
		hover:text-main-button-fg
		
		active:bg-main-button-bg--active 
		`,

		default: `border-border 
		bg-button-bg
		hover:bg-button-bg--hover
		active:bg-button-bg--active
		
		${baseVariantClasses}
		`,
		ghost: `${baseVariantClasses} 
		hover:bg-ghost-button-bg--hover
		active:bg-ghost-button-bg--active
		border-transparent
		bg-ghost-button-bg
		`,
	};

	const formClasses = {
		rounded: "rounded-3xl",
		square: "rounded-sm",
	};

	return (
		<button
			type="button"
			className={`${baseClasses} ${variantClasses[variant]} ${formClasses[form]} ${className}`}
			{...props}
		>
			{children}
		</button>
	);
};

export default RsdsButton;
