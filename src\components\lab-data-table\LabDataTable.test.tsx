import { fireEvent, render, screen } from "@testing-library/react";
import { describe, expect, it, vi } from "vitest";
import LabDataTable from "./LabDataTable";

const mockPatientInfo = {
	id: "19930303-2388",
	name: "<PERSON><PERSON>",
	gender: "<PERSON><PERSON><PERSON>",
	age: 32,
};

const mockRecords = [
	{
		date: "2025-03-31 09:32",
		orderedBy: "<PERSON> (l<PERSON>)",
		department: "Medicinsk avdelning",
		question: "Heptatit B?",
		analysisGroup: "HEPATIT-B",
		result: "Preliminärt",
		resultStatus: "Ovidimerat",
	},
	{
		date: "2025-02-13 14:05",
		orderedBy: "<PERSON><PERSON> (l<PERSON>)",
		department: "Infektionsavdelning Norrland",
		question: "HIV?",
		analysisGroup: "SEROLOGI",
		result: "Slutsvar",
		resultStatus: "Ovidimerat",
	},
];

describe("LabDataTable", () => {
	const defaultProps = {
		patientInfo: mockPatientInfo,
		records: mockRecords,
		totalRecords: 28,
		shownRecords: 2,
	};

	it("renders patient information correctly", () => {
		render(<LabDataTable {...defaultProps} />);

		expect(screen.getByText("19930303-2388")).toBeInTheDocument();
		expect(
			screen.getByText("Sofie Lindberg (Kvinna, 32 år)"),
		).toBeInTheDocument();
	});

	it("renders header with RS Labbdata title", () => {
		render(<LabDataTable {...defaultProps} />);

		expect(screen.getByText("RS Labbdata")).toBeInTheDocument();
		expect(
			screen.getByText("Tillgängliga provresultat från äldre journalsystem"),
		).toBeInTheDocument();
	});

	it("displays correct record count", () => {
		render(<LabDataTable {...defaultProps} />);

		expect(screen.getByText("Visar 2 av 28 provtagningar")).toBeInTheDocument();
	});

	it("renders table records correctly", () => {
		render(<LabDataTable {...defaultProps} />);

		expect(screen.getAllByText("2025-03-31 09:32")[0]).toBeInTheDocument();
		expect(
			screen.getAllByText("Sarah Lindahl (läkare)")[0],
		).toBeInTheDocument();
		expect(screen.getAllByText("Heptatit B?")[0]).toBeInTheDocument();
		expect(screen.getAllByText("HEPATIT-B")[0]).toBeInTheDocument();
	});

	it("calls onShowMore when 'Visa fler' button is clicked", () => {
		const onShowMore = vi.fn();
		render(<LabDataTable {...defaultProps} onShowMore={onShowMore} />);

		const showMoreButton = screen.getByText("Visa fler");
		fireEvent.click(showMoreButton);

		expect(onShowMore).toHaveBeenCalledTimes(1);
	});

	it("calls onShowAll when 'Visa alla' button is clicked", () => {
		const onShowAll = vi.fn();
		render(<LabDataTable {...defaultProps} onShowAll={onShowAll} />);

		const showAllButton = screen.getByText("Visa alla");
		fireEvent.click(showAllButton);

		expect(onShowAll).toHaveBeenCalledTimes(1);
	});

	it("calls onChangePatient when 'Byt patient' link is clicked", () => {
		const onChangePatient = vi.fn();
		render(
			<LabDataTable {...defaultProps} onChangePatient={onChangePatient} />,
		);

		const changePatientLink = screen.getByText("Byt patient");
		fireEvent.click(changePatientLink);

		expect(onChangePatient).toHaveBeenCalledTimes(1);
	});

	it("renders tabs correctly", () => {
		render(<LabDataTable {...defaultProps} />);

		expect(screen.getByText("Provtagningstillfällen")).toBeInTheDocument();
		expect(screen.getByText("Klinisk kemi")).toBeInTheDocument();
		expect(screen.getByText("Mikrobiologi")).toBeInTheDocument();
		expect(screen.getByText("Cytologi och Patologi")).toBeInTheDocument();
	});

	it("handles records without question field", () => {
		const recordsWithoutQuestion = [
			{
				date: "2025-02-13 14:05",
				orderedBy: "Eva Olsson (läkare)",
				department: "Ortopedisk avdelning Norrland",
				question: "",
				analysisGroup: "SÅR/SEKRET ODLING",
				result: "Slutsvar",
				resultStatus: "Ovidimerat",
			},
		];

		render(
			<LabDataTable
				{...defaultProps}
				records={recordsWithoutQuestion}
				shownRecords={1}
			/>,
		);

		expect(screen.getAllByText("SÅR/SEKRET ODLING")[0]).toBeInTheDocument();
	});
});
