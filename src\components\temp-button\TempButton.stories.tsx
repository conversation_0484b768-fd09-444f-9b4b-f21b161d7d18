import type { Meta, StoryObj } from "@storybook/react-vite";
import TempButton from "./TempButton";

const meta: Meta<typeof TempButton> = {
	title: "Other/Temp Button",
	component: TempButton,
	parameters: {
		layout: "centered",
	},
	tags: ["autodocs"],
	argTypes: {
		variant: {
			control: { type: "select" },
			options: ["primary", "secondary", "outline"],
		},
	},
};

export default meta;
type Story = StoryObj<typeof TempButton>;

export const Primary: Story = {
	args: {
		variant: "primary",
		children: "Button",
	},
};

export const Secondary: Story = {
	args: {
		variant: "secondary",
		children: "Button",
	},
};

export const Outline: Story = {
	args: {
		variant: "outline",
		children: "Button",
	},
};

export const Large: Story = {
	args: {
		children: "Large Button",
		className: "text-lg",
	},
};
