# https://pnpm.io/continuous-integration
parameters:
  - name: nodeVers<PERSON>
    default: '22.x'
  - name: pnpm_config_cache
    default: $(Pipeline.Workspace)/.pnpm-store

steps:
  #Authenticate for usage of artifact.
  - task: npmAuthenticate@0
    displayName: 'Authenticate to Azure Artifacts' # Or your registry
    inputs:
        workingFile: '.npmrc' # Ensure you have this if needed

  # Add this step to create the directory before the cache task
  - script: |
      mkdir -p "$PNPM_CACHE_DIR"
    displayName: Create pnpm cache directory
    env:
      PNPM_CACHE_DIR: $(pnpm_config_cache)
    # This step should run before the Cache task
  - task: Cache@2
    inputs:
      key: 'pnpm | "$(Agent.OS)" | pnpm-lock.yaml'
      path: $(pnpm_config_cache)
    displayName: Cache pnpm

  - task: NodeTool@0
    displayName: 'Install Node.js'
    inputs:
      versionSpec: ${{ parameters.nodeVersion }}

  - script: |
      npm install -g corepack@latest
      corepack enable pnpm
      corepack prepare pnpm@latest-10 --activate
      pnpm config set store-dir "$PNPM_CACHE_DIR"
    displayName: 'Setup pnpm'
    env:
      PNPM_CACHE_DIR: $(pnpm_config_cache)

  - script: |
      pnpm install --frozen-lockfile
    displayName: 'Install dependencies'
