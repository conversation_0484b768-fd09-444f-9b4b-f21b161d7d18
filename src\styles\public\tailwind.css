@import "tailwindcss";

:root{
	/* Brand */
	--Brand-cp-cream: rgba(253, 249, 228, 1);
	--Brand-cp-sea: rgba(48, 124, 142, 1);
	--Brand-cp-yellow: rgba(253, 211, 47, 1);
	--Brand-cp-red: rgba(228, 1, 53, 1);
	--Brand-cp-earth: rgba(95, 82, 54, 1);

	/* Tweaks */
	--Tweaks-cp-red--bright: rgba(255, 26, 78, 1);
	--Tweaks-cp-sea--dark: rgba(34, 87, 99, 1);
	--Tweaks-cp-sea--darker: rgba(2, 58, 71, 1);
	--Tweaks-cp-sea--bright: rgba(130, 214, 233, 1);
	--Tweaks-cp-sea--brighter: rgba(168, 252, 255, 1);
	--Tweaks-cp-sea--brightest: rgba(207, 255, 255, 1);
	--Tweaks-cp-saphire: rgba(1, 87, 173, 1);
	--Tweaks-cp-saphire--medium: rgba(1, 116, 233, 1);
	--Tweaks-cp-saphire--bright: rgba(98, 176, 254, 1);
	--Tweaks-cp-saphire--bright-alpha-33: rgba(98, 176, 254, 0.33);
	--Tweaks-cp-saphire--bright-alpha-12: rgba(98, 176, 254, 0.12);

	/* Gray scale */
	--Grey-scale-cp-black: rgba(0, 0, 0, 1);
	--Grey-scale-cp-black--900: rgba(0, 0, 0, 1);
	--Grey-scale-cp-grey--800: rgba(32, 32, 32, 1);
	--Grey-scale-cp-grey--darken-87: rgba(0, 0, 0, 0.87);
	--Grey-scale-cp-grey--700: rgba(64, 64, 64, 1);
	--Grey-scale-cp-grey--650: rgba(80, 80, 80, 1);
	--Grey-scale-cp-grey--600: rgba(96, 96, 96, 1);
	--Grey-scale-cp-grey--500: rgba(112, 112, 112, 1);
	--Grey-scale-cp-grey--darken-60: rgba(0, 0, 0, 0.6);
	--Grey-scale-cp-grey--400: rgba(128, 128, 128, 1);
	--Grey-scale-cp-grey--300: rgba(160, 160, 160, 1);
	--Grey-scale-cp-grey--200: rgba(192, 192, 192, 1);
	--Grey-scale-cp-grey--175: rgba(208, 208, 208, 1);
	--Grey-scale-cp-grey--150: rgba(224, 224, 224, 1);
	--Grey-scale-cp-grey--darken-12: rgba(0, 0, 0, 0.12);
	--Grey-scale-cp-grey--darken-6: rgba(0, 0, 0, 0.06);
	--Grey-scale-cp-grey--100: rgba(240, 240, 240, 1);
	--Grey-scale-cp-grey--lighten-38: rgba(255, 255, 255, 0.38);
	--Grey-scale-cp-grey--lighten-25: rgba(255, 255, 255, 0.25);
	--Grey-scale-cp-white: rgba(255, 255, 255, 1);
	--Grey-scale-cp-transparent: rgba(0, 0, 0, 0);

	/* 1177 */
	--1177-cp-1177-sky-dark: rgba(59, 66, 102, 1);
	--1177-cp-1177-sky-base: rgba(57, 98, 145, 1);
	--1177-cp-1177-sky-clear: rgba(0, 151, 227, 1);
	--1177-cp-1177-sky-line: rgba(198, 210, 223, 1);
	--1177-cp-1177-sky-background: rgba(241, 241, 245, 1);
	--1177-cp-1177-grass-dark: rgba(241, 241, 245, 1);
	--1177-cp-1177-grass-base: rgba(65, 144, 2, 1);
	--1177-cp-1177-grass-clear: rgba(111, 187, 43, 1);
	--1177-cp-1177-grass-line: rgba(207, 222, 191, 1);
	--1177-cp-1177-grass-background: rgba(238, 248, 238, 1);
	--1177-cp-1177-plum-dark: rgba(89, 36, 76, 1);
	--1177-cp-1177-plum-base: rgba(244, 115, 159, 1);
	--1177-cp-1177-plum-clear: rgba(244, 115, 159, 1);
	--1177-cp-1177-plum-line: rgba(245, 201, 216, 1);
	--1177-cp-1177-plum-background: rgba(246, 238, 246, 1);
	--1177-cp-1177-sun-dark: rgba(246, 238, 246, 1);
	--1177-cp-1177-sun-base: rgba(250, 129, 0, 1);
	--1177-cp-1177-sun-clear: rgba(255, 193, 0, 1);
	--1177-cp-1177-sun-background: rgba(255, 248, 224, 1);
	--1177-cp-1177-stone-dark: rgba(53, 53, 53, 1);
	--1177-cp-1177-stone-base: rgba(99, 100, 102, 1);
	--1177-cp-1177-stone-clear: rgba(128, 130, 133, 1);
	--1177-cp-1177-stone-line: rgba(218, 219, 220, 1);
	--1177-cp-1177-stone-background: rgba(241, 242, 242, 1);

	/* Color theme ct */
	--ct-foreground: var(--Grey-scale-cp-grey--800);
	--ct-foreground--subdued: var(--Grey-scale-cp-grey--400);
	--ct-background: var(--Grey-scale-cp-white);
	--ct-border: var(--Grey-scale-cp-grey--800);
	--ct-border-subdued:var(--Grey-scale-cp-grey--400);
	--ct-link: var(--Tweaks-cp-saphire);
	--ct-border--selected: var(--Tweaks-cp-saphire);
	--ct-link--visited: var(--1177-cp-1177-plum-base);
	--ct-bg--selected: var(--Tweaks-cp-saphire--bright-alpha-33);
	--ct-card-bg--hover: var(--Tweaks-cp-saphire--bright-alpha-12);
	--ct-card-bg--active: var(--Tweaks-cp-saphire--bright-alpha-33);

	--ct-focus: var(--Tweaks-cp-saphire--medium);
	--ct-bg--active: var(--Grey-scale-cp-grey--darken-12);
	--ct-bg--hover: var(--Grey-scale-cp-grey--darken-6);
	--ct-warning: var(--Brand-cp-red);

	/* Logo */
	--ct-logo-primary: var(--Brand-cp-red);
	--ct-logo-secondar: var(--Brand-cp-yellow);
	--ct-logo-black-gray-primary: rgba(32, 32, 32, 0.5);
	--ct-logo-black-gray-secondary: var(--Grey-scale-cp-grey--800);

	/* menu item */
	--ct-menu-item-bg: var(--Grey-scale-cp-white);
	--ct-menu-item-bg--hover: var(--Grey-scale-cp-grey--100);
	--ct-menu-item-outline: var(--Grey-scale-cp-white);
	--ct-menu-item-underline: var(--Grey-scale-cp-transparent);
	--ct-menu-item-underline--hover: var(--Brand-cp-yellow);
	--ct-menu-item-underline--active: var(--Brand-cp-yellow);

	/* Button */
	--ct-button-bg: var(--Grey-scale-cp-white);
	--ct-button-bg--hover: var(--Grey-scale-cp-grey--100);
	--ct-button-bg--active: var(--Grey-scale-cp-grey--175);
	--ct-button-bg--selected: var(--Tweaks-cp-saphire--bright-alpha-33);
	
	--ct-main-button-fg: var(--Brand-cp-cream);
	--ct-main-button-fg--disabled: var(--Grey-scale-cp-grey--darken-60);
	--ct-main-button-bg: var(--Brand-cp-sea);
	--ct-main-button-bg--disabled: var(--Grey-scale-cp-grey--200);
	--ct-main-button-bg--hover: var(--Tweaks-cp-sea--dark);

	--ct-main-button-bg--active: var(--Tweaks-cp-sea--darker);
	--ct-ghost-button-bg: var(--Grey-scale-cp-transparent);
	--ct-ghost-button-bg--hover: var(--Grey-scale-cp-grey--darken-6);
	--ct-ghost-button-bg--active: var(--Grey-scale-cp-grey--darken-12);
	--ct-ghost-button-bg--selected: var(--Tweaks-cp-saphire--bright-alpha-33);
}

@custom-variant dark (&:where(.dark, .dark *));


@theme static {
	--color-\*: initial;
	/* Brand */
	--color-brand-cream: var(--Brand-cp-cream);
	--color-brand-sea: var(--Brand-cp-sea);
	--color-brand-yellow: var(--Brand-cp-yellow);
	--color-brand-red: var(--Brand-cp-red);
	--color-brand-earth: var(--Brand-cp-earth);

	/* Tweaks */
	--color-tweaks-cp-red: var(--Tweaks-cp-red--bright);

	--color-tweaks-sea-50: var(--Tweaks-cp-sea--brightest);
	--color-tweaks-sea-100: var(--Tweaks-cp-sea--brighter);
	--color-tweaks-sea-200: var(--Tweaks-cp-sea--bright);
	--color-tweaks-sea-300: var(--Tweaks-cp-sea--dark);
	--color-tweaks-sea-400: var(--Tweaks-cp-sea--darker);

	--color-tweaks-saphire-50: var(--Tweaks-cp-saphire--bright-alpha-12);
	--color-tweaks-saphire-100: var(--Tweaks-cp-saphire--bright-alpha-33);
	--color-tweaks-saphire-200: var(--Tweaks-cp-saphire--bright);
	--color-tweaks-saphire-300: var(--Tweaks-cp-saphire);
	--color-tweaks-saphire-400: var(--Tweaks-cp-saphire--medium);

	/* White */
	--color-white: var(--Grey-scale-cp-white);
	--color-white\/38: var(--Grey-scale-cp-grey--lighten-38);
	--color-white\/25: var(--Grey-scale-cp-grey--lighten-25);

	/* Transparent */
	--color-transparent: var(--Grey-scale-cp-transparent);

	/* Gray scale */
	--color-gray-100: var(--Grey-scale-cp-grey--100);
	--color-gray-150: var(--Grey-scale-cp-grey--150);
	--color-gray-175: var(--Grey-scale-cp-grey--175);
	--color-gray-200: var(--Grey-scale-cp-grey--200);
	--color-gray-300: var(--Grey-scale-cp-grey--300);
	--color-gray-400: var(--Grey-scale-cp-grey--400);
	--color-gray-500: var(--Grey-scale-cp-grey--500);
	--color-gray-600: var(--Grey-scale-cp-grey--600);
	--color-gray-650: var(--Grey-scale-cp-grey--650);
	--color-gray-700: var(--Grey-scale-cp-grey--700);
	--color-gray-800: var(--Grey-scale-cp-grey--800);

	/* Black */
	--color-black: var(--Grey-scale-cp-black);
	--color-black\/87: var(--Grey-scale-cp-grey--darken-87);
	--color-black\/6: var(--Grey-scale-cp-grey--darken-6);
	--color-black\/12: var(--Grey-scale-cp-grey--darken-12);

/* TODO WTF what is the naming conventions here and where is it used*/
	/* 1177 */
	--color-1177-sky-base: var(--1177-cp-1177-sky-base);
	--color-1177-sky-clear: var(--1177-cp-1177-sky-clear);
	--color-1177-sky-line: var(--1177-cp-1177-sky-line);
	--color-1177-sky-background: var(--1177-cp-1177-sky-background);
	--color-1177-sky-dark: var(--1177-cp-1177-sky-dark);

	--color-1177-grass-dark: var(--1177-cp-1177-grass-dark);
	--color-1177-grass-base: var(--1177-cp-1177-grass-base);
	--color-1177-grass-clear: var(--1177-cp-1177-grass-clear);
	--color-1177-grass-line: var(--1177-cp-1177-grass-line);
	--color-1177-grass-background: var(--1177-cp-1177-grass-background);

	--color-1177-plum-dark: var(--1177-cp-1177-plum-dark);
	--color-1177-plum-base: var(--1177-cp-1177-plum-base);
	--color-1177-plum-clear: var(--1177-cp-1177-plum-clear);
	--color-1177-plum-line: var(--1177-cp-1177-plum-line);
	--color-1177-plum-background: var(--1177-cp-1177-plum-background);

	--color-1177-sun-dark: var(--1177-cp-1177-sun-dark);
	--color-1177-sun-base: var(--1177-cp-1177-sun-base);
	--color-1177-sun-clear: var(--1177-cp-1177-sun-clear);
	--color-1177-sun-background: var(--1177-cp-1177-sun-background);

	--color-1177-stone-dark: var(--1177-cp-1177-stone-dark);
	--color-1177-stone-base: var(--1177-cp-1177-stone-base);
	--color-1177-stone-clear: var(--1177-cp-1177-stone-clear);
	--color-1177-stone-line: var(--1177-cp-1177-stone-line);
	--color-1177-stone-background: var(--1177-cp-1177-stone-background);

/* end of WTF */

	/* Color theme ct */
	--color-foreground: var(--color-gray-800);
	--color-foreground-subdued: var(--color-gray-400);

	--color-background: var(--color-white);
	
	--color-border: var(--color-gray-800);
	--color-border-selected: var(--color-tweaks-saphire-300);

	--color-link: var(--color-tweaks-saphire-300);

	--color-link--visited: var(--color-tweaks-saphire-200);
	--color-bg--selected: var(--color-tweaks-saphire-300);
	--color-card-bg--hover: var(--color-tweaks-saphire-50);
	--color-card-bg--active: var(--color-tweaks-saphire-100);
	--color-focus: var(--color-tweaks-saphire-400);
	--color-bg--active: var(--color-black\/12);
	--color-bg--hover: var(--color-black\/6);
	--color-warning: var(--color-tweaks-cp-red);
	--color-logo-primary: var(--color-brand-red);
	--color-logo-secondar: var(--color-brand-yellow);
	--color-logo-black-gray-primary: rgba(32, 32, 32, 0.5);
	--color-logo-black-gray-secondary: var(--color-gray-800);
	/* menu item */
	--color-ct-menu-item-bg: var(--color-white);
	--color-ct-menu-item-bg--hover: var(--color-gray-100);
	--color-ct-menu-item-outline: var(--color-white);
	--color-ct-menu-item-underline: var(--color-transparent);
	--color-ct-menu-item-underline--hover: var(--color-brand-yellow);
	--color-ct-menu-item-underline--active: var(--color-brand-yellow);

	/* Button */
	--color-ct-button-bg: var(--color-white);
	--color-ct-button-bg--hover: var(--color-gray-100);
	--color-ct-button-bg--active: var(--color-gray-175);
	--color-ct-button-bg--selected: var(--color-tweaks-saphire-100);
	--color-ct-main-button-fg: var(--color-brand-cream);
	--color-ct-main-button-fg--disabled: var(--color-black\/87);
	--color-ct-main-button-bg: var(--color-brand-sea);
	--color-ct-main-button-bg--disabled: var(--color-gray-200);
	--color-ct-main-button-bg--hover: var(--color-tweaks-sea-300);
	--color-ct-main-button-bg--active: var(--color-tweaks-sea-400);
	--color-ct-ghost-button-bg: var(--color-transparent);
	--color-ct-ghost-button-bg--hover: var(--color-black\/6);
	--color-ct-ghost-button-bg--active: var(--color-black\/12);
	--color-ct-ghost-button-bg--selected: var(--color-tweaks-saphire-100);

	/* break points */
	--breakpoint-sm: 19rem;
	--breakpoint-md: 48rem;
	--breakpoint-lg: 80rem;
	--breakpoint-xl: 110.5rem;
	--breakpoint-2xl: 135rem;

	--container-3xs: 16rem;
	--container-2xs: 18rem;
	--container-xs: 20rem;
	--container-sm: 24rem;
	--container-md: 28rem;
	--container-lg: 32rem;
	--container-xl: 36rem;
	--container-2xl: 42rem;
	--container-3xl: 48rem;
	--container-4xl: 56rem;
	--container-5xl: 64rem;
	--container-6xl: 72rem;
	--container-7xl: 80rem;
}


.dark {
	--color-ct-foreground: var(--color-cp-white);
	--color-ct-foreground--subdued: var(--color-cp-grey--300);
	--color-ct-background: var(--color-cp-grey--800);
	--color-ct-border: var(--color-cp-white);
	--color-ct-link: var(--color-cp-saphire--bright);
	--color-ct-border--selected: var(--color-cp-saphire--bright);
	--color-ct-link--visited: var(--color-cp-saphire--bright);
	--color-ct-bg--selected: var(--color-cp-saphire--bright);
	--color-ct-card-bg--hover: var(--color-cp-saphire--bright-alpha-12);
	--color-ct-card-bg--active: var(--color-cp-saphire--bright-alpha-33);
	--color-ct-focus: var(--color-cp-saphire--bright);
	--color-ct-bg--active: var(--color-cp-grey--light-38);
	--color-ct-bg--hover: var(--color-cp-grey--light-25);
	--color-ct-warning: var(--color-cp-red--bright);
	--color-ct-logo-primary: var(--color-cp-white);
	--color-ct-logo-secondar: var(--color-cp-white);

	/* menu item */
	--color-ct-menu-item-bg: var(--color-cp-grey--600);
	--color-ct-menu-item-bg--hover: var(--color-cp-grey--800);
	--color-ct-menu-item-outline: var(--color-cp-white);
	--color-ct-menu-item-underline--hover: var(--color-cp-white);
	--color-ct-menu-item-underline--active: var(--color-cp-white);

	/* Button */
	--color-ct-button-bg: var(--color-cp-grey--800);
	--color-ct-button-bg--hover: var(--color-cp-grey--650);
	--color-ct-button-bg--active: var(--color-cp-grey--500);
	--color-ct-button-bg--selected: var(--color-cp-saphire--bright-alpha-33);
	--color-ct-main-button-fg: var(--color-cp-grey--darken-87);
	--color-ct-main-button-fg--disabled: var(--color-cp-grey--darken-60);
	--color-ct-main-button-bg: var(--color-cp-sea--bright);
	--color-ct-main-button-bg--disabled: var(--color-cp-grey--200);
	--color-ct-main-button-bg--hover: var(--color-cp-sea--brighter);
	--color-ct-main-button-bg--active: var(--color-cp-sea--brightest);
	--color-ct-ghost-button-bg: var(--color-cp-transparent);
	--color-ct-ghost-button-bg--hover: var(--color-cp-grey--25);
	--color-ct-ghost-button-bg--active: var(--color-cp-grey--light-38);
	--color-ct-ghost-button-bg--selected: var(
		--color-cp-saphire--bright-alpha-33
	);
}

:root {
	--background: var(--color-ct-background);
	--foreground: var(--color-ct-foreground);
}

@theme inline {
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--font-sans: "Public Sans";
	--font-mono: "Public Sans";
}


@media (prefers-color-scheme: dark) {
	:root {
		--background: var(--color-ct-background);
		--foreground: var(--color-ct-foreground);

		--color-ct-foreground: var(--color-cp-white);
		--color-ct-foreground--subdued: var(--color-cp-grey--300);
		--color-ct-background: var(--color-cp-grey--800);
		--color-ct-border: var(--color-cp-white);
		--color-ct-link: var(--color-cp-saphire--bright);
		--color-ct-border--selected: var(--color-cp-saphire--bright);
		--color-ct-link--visited: var(--color-cp-saphire--bright);
		--color-ct-bg--selected: var(--color-cp-saphire--bright);
		--color-ct-card-bg--hover: var(--color-cp-saphire--bright-alpha-12);
		--color-ct-card-bg--active: var(--color-cp-saphire--bright-alpha-33);
		--color-ct-focus: var(--color-cp-saphire--bright);
		--color-ct-bg--active: var(--color-cp-grey--light-38);
		--color-ct-bg--hover: var(--color-cp-grey--light-25);
		--color-ct-warning: var(--color-cp-red--bright);
		--color-ct-logo-primary: var(--color-cp-white);
		--color-ct-logo-secondar: var(--color-cp-white);
		--color-ct-logo-black-gray-primary: rgba(255, 255, 255, 0.5);
		--color-ct-logo-black-gray-secondary: var(--color-cp-white);

		/* menu item */
		--color-ct-menu-item-bg: var(--color-cp-grey--600);
		--color-ct-menu-item-bg--hover: var(--color-cp-grey--800);
		--color-ct-menu-item-outline: var(--color-cp-white);
		--color-ct-menu-item-underline--hover: var(--color-cp-white);
		--color-ct-menu-item-underline--active: var(--color-cp-white);

		/* Button */
		--color-ct-button-bg: var(--color-cp-grey--800);
		--color-ct-button-bg--hover: var(--color-cp-grey--650);
		--color-ct-button-bg--active: var(--color-cp-grey--500);
		--color-ct-button-bg--selected: var(--color-cp-saphire--bright-alpha-33);
		--color-ct-main-button-fg: var(--color-cp-grey--darken-87);
		--color-ct-main-button-fg--disabled: var(--color-cp-grey--darken-60);
		--color-ct-main-button-bg: var(--color-cp-sea--bright);
		--color-ct-main-button-bg--disabled: var(--color-cp-grey--200);
		--color-ct-main-button-bg--hover: var(--color-cp-sea--brighter);
		--color-ct-main-button-bg--active: var(--color-cp-sea--brightest);
		--color-ct-ghost-button-bg: var(--color-cp-transparent);
		--color-ct-ghost-button-bg--hover: var(--color-cp-grey--25);
		--color-ct-ghost-button-bg--active: var(--color-cp-grey--light-38);
		--color-ct-ghost-button-bg--selected: var(
			--color-cp-saphire--bright-alpha-33
		);
	}
}

body {
	background: var(--background);
	color: var(--foreground);
}
