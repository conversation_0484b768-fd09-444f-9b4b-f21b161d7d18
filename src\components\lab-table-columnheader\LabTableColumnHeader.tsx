import type { HTMLAttributes, ReactNode } from "react";
import "../../styles/public/tailwind.css";

export interface LabTableColumnHeaderProps
	extends Omit<HTMLAttributes<HTMLTableCellElement>, "children"> {
	/**
	 * Cell content
	 */
	children: ReactNode;
	/**
	 * Type of table cell
	 */
	type?: "th" | "td";
	/**
	 * Text alignment within the cell
	 */
	textAlign?: "left" | "center" | "right";
	/**
	 * Additional CSS classes
	 */
	className?: string;
}

/**
 * Table cell component for both header (TH) and data (TD) cells
 */
export const LabTableColumnHeader = ({
	children,
	type = "td",
	textAlign = "left",
	className = "",
	...props
}: LabTableColumnHeaderProps) => {
	const Component = type;

	const baseClasses =
		"flex flex-col justify-center px-4 py-3 border-b border-ct-border";

	const typeClasses = {
		th: "border-t-2 border-t-ct-border font-bold text-base leading-6",
		td: "font-normal text-sm leading-5",
	};

	const alignmentClasses = {
		left: "items-start text-left",
		center: "items-center text-center",
		right: "items-end text-right",
	};

	return (
		<Component
			className={`
				${baseClasses} 
				${typeClasses[type]} 
				${alignmentClasses[textAlign]} 
				text-ct-foreground
				${className}
			`
				.trim()
				.replace(/\s+/g, " ")}
			{...props}
		>
			<span className="w-full">{children}</span>
		</Component>
	);
};

export default LabTableColumnHeader;
