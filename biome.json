{"$schema": "https://biomejs.dev/schemas/2.2.5/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "includes": ["**", "!**/dist", "!**/coverage", "!**/.changeset", "!**/.qodo", "!**/change", "!**/dist", "!**/storybook-static", "!**/node_modules"]}, "formatter": {"enabled": true, "indentStyle": "tab", "lineEnding": "lf"}, "assist": {"actions": {"source": {"organizeImports": "on"}}}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noParameterAssign": "error", "useAsConstAssertion": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useSelfClosingElements": "error", "useSingleVarDeclarator": "error", "noUnusedTemplateLiteral": "error", "useNumberNamespace": "error", "noInferrableTypes": "error", "noUselessElse": "error"}, "suspicious": {"noUnknownAtRules": "off"}}, "includes": ["**", "!**/dist/**", "!**/coverage/**", "!**/storybook-static"]}, "javascript": {"formatter": {"quoteStyle": "double"}}}