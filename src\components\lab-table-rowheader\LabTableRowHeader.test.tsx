import { render, screen } from "@testing-library/react";
import { describe, expect, it } from "vitest";
import { LabTableRowHeader } from "./LabTableRowHeader";

describe("TableRowHeader", () => {
	it("renders with default props", () => {
		render(
			<table>
				<thead>
					<tr>
						<LabTableRowHeader>Test Header</LabTableRowHeader>
					</tr>
				</thead>
			</table>,
		);

		const header = screen.getByRole("columnheader");
		expect(header).toBeInTheDocument();
		expect(header).toHaveTextContent("Test Header");
	});

	it("renders with left alignment by default", () => {
		render(
			<table>
				<thead>
					<tr>
						<LabTableRowHeader>Test Header</LabTableRowHeader>
					</tr>
				</thead>
			</table>,
		);

		const header = screen.getByRole("columnheader");
		expect(header).toHaveClass("items-start", "text-left");
	});

	it("renders with center alignment", () => {
		render(
			<table>
				<thead>
					<tr>
						<LabTableRowHeader textAlign="center">
							Test Header
						</LabTableRowHeader>
					</tr>
				</thead>
			</table>,
		);

		const header = screen.getByRole("columnheader");
		expect(header).toHaveClass("items-center", "text-center");
	});

	it("renders with right alignment", () => {
		render(
			<table>
				<thead>
					<tr>
						<LabTableRowHeader textAlign="right">Test Header</LabTableRowHeader>
					</tr>
				</thead>
			</table>,
		);

		const header = screen.getByRole("columnheader");
		expect(header).toHaveClass("items-end", "text-right");
	});

	it("applies custom className", () => {
		render(
			<table>
				<thead>
					<tr>
						<LabTableRowHeader className="custom-class">
							Test Header
						</LabTableRowHeader>
					</tr>
				</thead>
			</table>,
		);

		const header = screen.getByRole("columnheader");
		expect(header).toHaveClass("custom-class");
	});

	it("passes through other HTML attributes", () => {
		render(
			<table>
				<thead>
					<tr>
						{/** biome-ignore lint/correctness/useUniqueElementIds: For testing purposes */}
						<LabTableRowHeader data-testid="custom-header" id="header-1">
							Test Header
						</LabTableRowHeader>
					</tr>
				</thead>
			</table>,
		);

		const header = screen.getByTestId("custom-header");
		expect(header).toHaveAttribute("id", "header-1");
	});

	it("has proper styling classes", () => {
		render(
			<table>
				<thead>
					<tr>
						<LabTableRowHeader>Test Header</LabTableRowHeader>
					</tr>
				</thead>
			</table>,
		);

		const header = screen.getByRole("columnheader");
		expect(header).toHaveClass(
			"inline-flex",
			"h-16",
			"px-4",
			"py-3",
			"flex-col",
			"justify-end",
			"flex-shrink-0",
			"border-b-4",
			"border-ct-foreground",
		);
	});
});
