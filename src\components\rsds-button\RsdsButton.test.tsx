import { fireEvent, render, screen } from "@testing-library/react";
import { describe, expect, it, vi } from "vitest";
import { RsdsButton } from "./RsdsButton";

describe("Button", () => {
	it("renders correctly", () => {
		render(<RsdsButton>Click me</RsdsButton>);

		expect<HTMLElement>(screen.getByText("Click me")).toBeInTheDocument();
	});

	it("calls onClick when clicked", () => {
		const handleClick = vi.fn();
		render(<RsdsButton onClick={handleClick}>Click me</RsdsButton>);
		fireEvent.click(screen.getByText("Click me"));
		expect(handleClick).toHaveBeenCalledTimes(1);
	});

	it("does not call onClick when disabled", () => {
		const handleClick = vi.fn();
		render(
			<RsdsButton onClick={handleClick} disabled={true}>
				Click me
			</RsdsButton>,
		);
		fireEvent.click(screen.getByText("Click me"));
		expect(handleClick).not.toHaveBeenCalled();
	});

	it("applies the correct variant class", () => {
		const { rerender } = render(<RsdsButton variant="main">Main</RsdsButton>);
		expect<HTMLButtonElement>(screen.getByText("Main")).toHaveClass(
			"bg-ct-main-button-bg",
		);

		rerender(<RsdsButton variant="default">Default</RsdsButton>);
		expect(screen.getByText("Default")).toHaveClass("bg-ct-button-bg");
	});

	it("applies the correct size class", () => {
		render(<RsdsButton form="square">square</RsdsButton>);
		expect(screen.getByText("square")).toHaveClass("rounded-sm");

		render(<RsdsButton form="rounded">rounded</RsdsButton>);
		expect(screen.getByText("rounded")).toHaveClass("rounded-full");
	});
});
