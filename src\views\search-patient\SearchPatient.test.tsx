import { render, screen } from "@testing-library/react";
import { describe, expect, it, vi } from "vitest";
import type { LabHeaderMenuProps } from "../../components/lab-header-menu/LabHeaderMenu";
import type { LabSearchInputProps } from "../../components/lab-search-input/LabSearchInput";
import { SearchPatient } from "./SearchPatient";

// Mock the child components
vi.mock("../../components/lab-header-menu/LabHeaderMenu", () => ({
	default: ({ title, description, ...props }: LabHeaderMenuProps) => (
		<div data-testid="lab-header-menu" {...props}>
			<div data-testid="header-title">{title}</div>
			<div data-testid="header-description">{description}</div>
		</div>
	),
}));

vi.mock("../../components/lab-search-input/LabSearchInput", () => ({
	default: ({
		name,
		placeholder,
		value,
		onChange,
		onSearch,
		...props
	}: LabSearchInputProps) => (
		<div data-testid="lab-search-input" {...props}>
			<input
				data-testid="search-input"
				name={name}
				placeholder={placeholder}
				value={value}
				onChange={(e) => onChange?.(e.target.value)}
			/>
			<button
				type="button"
				data-testid="search-button"
				onClick={() => onSearch?.(value)}
			>
				Search
			</button>
		</div>
	),
}));

// Mock console.log to avoid noise in tests
const mockConsoleLog = vi.spyOn(console, "log").mockImplementation(() => {});

describe("SearchPatient", () => {
	afterEach(() => {
		mockConsoleLog.mockClear();
	});

	it("renders correctly with all main elements", () => {
		render(<SearchPatient />);

		// Check for header menu
		expect(screen.getByTestId("lab-header-menu")).toBeInTheDocument();
		expect(screen.getByTestId("header-title")).toHaveTextContent(
			"Labsvar i Skåne",
		);
		expect(screen.getByTestId("header-description")).toHaveTextContent(
			"Du är inloggad som Sarah Wingaard, VoB Internmedicinsk vård Ystad",
		);

		// Check for search input
		expect(screen.getByTestId("lab-search-input")).toBeInTheDocument();
	});

	it("renders search form with proper labels and instructions", () => {
		render(<SearchPatient />);

		// Check for main label
		expect(screen.getByText("Sök patient")).toBeInTheDocument();

		// Check for instructions
		expect(
			screen.getByText("Ange personnummer med 12 siffror (ÅÅÅÅMMDD-XXXX)"),
		).toBeInTheDocument();

		// Check that label exists (the actual htmlFor attribute is in the real component)
		const labelElement = screen.getByText("Sök patient").parentElement;
		expect(labelElement?.tagName.toLowerCase()).toBe("label");
	});

	it("renders search input with correct props", () => {
		render(<SearchPatient />);

		const searchInput = screen.getByTestId("lab-search-input");
		expect(searchInput).toBeInTheDocument();

		const input = screen.getByTestId("search-input");
		expect(input).toHaveAttribute("name", "pt");
		expect(input).toHaveAttribute("placeholder", "");
		expect(input).toHaveAttribute("value", "");
	});

	it("has proper CSS structure and classes", () => {
		render(<SearchPatient />);

		// Check main container
		const mainContainer = screen.getByTestId("lab-header-menu").parentElement;
		expect(mainContainer).toHaveClass("w-full");

		// Check content container classes
		const contentContainer = screen
			.getByText("Sök patient")
			.closest(".pl-\\[142px\\]");
		expect(contentContainer).toHaveClass(
			"pl-[142px]",
			"border-b",
			"p-9",
			"flex",
			"flex-col",
			"gap-y-3",
		);
	});

	it("handles search input changes correctly", () => {
		render(<SearchPatient />);

		const input = screen.getByTestId("search-input");

		// Simulate typing in the input
		input.dispatchEvent(new Event("change", { bubbles: true }));

		// The onChange handler should be called (mocked console.log)
		// We can't easily test the actual console.log call without more complex mocking
		expect(input).toBeInTheDocument();
	});

	it("handles search button click correctly", () => {
		render(<SearchPatient />);

		const searchButton = screen.getByTestId("search-button");

		// Simulate clicking the search button
		searchButton.click();

		// The onSearch handler should be called (mocked console.log)
		expect(searchButton).toBeInTheDocument();
	});

	it("renders with proper accessibility structure", () => {
		render(<SearchPatient />);

		// Check that the label is properly structured
		const labelElement = screen.getByText("Sök patient").parentElement;
		expect(labelElement?.tagName.toLowerCase()).toBe("label");

		// Check that the input has the correct name attribute
		const input = screen.getByTestId("search-input");
		expect(input).toHaveAttribute("name", "pt");
	});

	it("renders header menu with correct props", () => {
		render(<SearchPatient />);

		const headerTitle = screen.getByTestId("header-title");
		const headerDescription = screen.getByTestId("header-description");

		expect(headerTitle).toHaveTextContent("Labsvar i Skåne");
		expect(headerDescription).toHaveTextContent(
			"Du är inloggad som Sarah Wingaard, VoB Internmedicinsk vård Ystad",
		);
	});

	it("has proper layout structure", () => {
		render(<SearchPatient />);

		// Check that the main container has full width
		const mainContainer = screen.getByTestId("lab-header-menu").parentElement;
		expect(mainContainer).toHaveClass("w-full");

		// Check that content is properly indented
		const contentDiv =
			screen.getByText("Sök patient").parentElement?.parentElement;
		expect(contentDiv).toHaveClass("pl-[142px]");
	});
});
