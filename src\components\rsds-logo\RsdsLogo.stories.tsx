import type { <PERSON>a, StoryObj } from "@storybook/react-vite";
import RsdsLogo from "./RsdsLogo";

const meta: Meta<typeof RsdsLogo> = {
	title: "UI/Rsds Logo",
	component: RsdsLogo,
	parameters: {
		layout: "centered",
	},
	tags: ["autodocs"],
	argTypes: {
		variant: {
			control: { type: "select" },
			options: ["color", "black-gray", "white-darkmode", "black-gray-darkmode"],
		},
		size: {
			control: { type: "select" },
			options: ["sm", "md", "lg", "xl"],
		},
	},
};

export default meta;
type Story = StoryObj<typeof RsdsLogo>;

export const Default: Story = {
	args: {
		variant: "color",
		size: "md",
		alt: "Region Skåne logo",
	},
};
export const BlackAndGray: Story = {
	args: {
		variant: "black-gray",
		size: "md",
		alt: "Region Skåne logo",
	},
};

export const AllVariants: Story = {
	render: () => (
		<div className="grid grid-cols-2 gap-8 p-8">
			<div className="flex flex-col items-center gap-4 rounded-lg bg-white p-6 border">
				<RsdsLogo variant="color" size="md" />
				<p className="font-medium text-sm">Color (Default)</p>
				<p className="text-gray-500 text-xs">Should show yellow and red</p>
				<div className="flex gap-2 text-xs">
					<div
						className="w-4 h-4 rounded"
						style={{ backgroundColor: "var(--color-cp-yellow)" }}
					/>
					<div
						className="w-4 h-4 rounded"
						style={{ backgroundColor: "var(--color-cp-red)" }}
					/>
				</div>
			</div>

			<div className="flex flex-col items-center gap-4 rounded-lg bg-white p-6 border">
				<RsdsLogo variant="black-gray" size="md" />
				<p className="font-medium text-sm">Black & Gray</p>
				<p className="text-gray-500 text-xs">For light backgrounds</p>
			</div>

			<div className="flex flex-col items-center gap-4 rounded-lg bg-gray-900 p-6">
				<RsdsLogo variant="white-darkmode" size="md" />
				<p className="font-medium text-sm text-white">White (Dark Mode)</p>
				<p className="text-gray-400 text-xs">For dark backgrounds</p>
			</div>

			<div className="flex flex-col items-center gap-4 rounded-lg bg-gray-900 p-6">
				<RsdsLogo variant="black-gray-darkmode" size="md" />
				<p className="font-medium text-sm text-white">
					Black & Gray (Dark Mode)
				</p>
				<p className="text-gray-400 text-xs">For dark backgrounds</p>
			</div>
		</div>
	),
};

export const AllSizes: Story = {
	render: () => (
		<div className="flex items-end gap-8 p-8">
			<div className="flex flex-col items-center gap-2">
				<RsdsLogo variant="color" size="sm" />
				<p className="text-xs">Small</p>
			</div>

			<div className="flex flex-col items-center gap-2">
				<RsdsLogo variant="color" size="md" />
				<p className="text-xs">Medium</p>
			</div>

			<div className="flex flex-col items-center gap-2">
				<RsdsLogo variant="color" size="lg" />
				<p className="text-xs">Large</p>
			</div>

			<div className="flex flex-col items-center gap-2">
				<RsdsLogo variant="color" size="xl" />
				<p className="text-xs">Extra Large</p>
			</div>
		</div>
	),
};

export const CustomStyling: Story = {
	render: () => (
		<div className="flex gap-8 p-8">
			<RsdsLogo
				variant="color"
				size="md"
				className="cursor-pointer opacity-75 transition-opacity hover:opacity-100"
			/>
			<RsdsLogo variant="black-gray" size="lg" className="drop-shadow-lg" />
		</div>
	),
};

export const ResponsiveUsage: Story = {
	render: () => (
		<div className="p-8">
			<RsdsLogo
				variant="color"
				className="h-auto w-12 sm:w-16 md:w-20 lg:w-24"
				alt="Responsive RS Design System RsdsLogo"
			/>
			<p className="mt-4 text-muted-foreground text-sm">
				This RsdsLogo uses responsive classes to scale with screen size
			</p>
		</div>
	),
};
