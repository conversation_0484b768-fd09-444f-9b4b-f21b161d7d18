import type { <PERSON>a, StoryObj } from "@storybook/react-vite";
import LabDataTable from "./LabDataTable";

const meta: Meta<typeof LabDataTable> = {
	title: "View/Lab Data Table",
	component: LabDataTable,
	parameters: {
		layout: "fullscreen",
	},
	tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof LabDataTable>;

const samplePatientInfo = {
	id: "19930303-2388",
	name: "<PERSON><PERSON>",
	gender: "<PERSON><PERSON><PERSON>",
	age: 32,
};

const sampleRecords = [
	{
		date: "2025-03-31 09:32",
		orderedBy: "<PERSON> (l<PERSON>)",
		department: "Medicinsk avdelning",
		question: "Heptatit B?",
		analysisGroup: "HEPATIT-B",
		result: "Preliminärt",
		resultStatus: "Ovidimerat",
	},
	{
		date: "2025-02-13 14:05",
		orderedBy: "<PERSON><PERSON> (<PERSON>)",
		department: "Infektionsavdelning Norrland",
		question: "HIV?",
		analysisGroup: "SEROLOGI",
		result: "<PERSON>lut<PERSON>var",
		resultStatus: "Ovidimerat",
	},
	{
		date: "2025-02-13 14:05",
		orderedBy: "<PERSON>",
		department: "Akutavdelningen Dalarna",
		question: "Infektion?",
		analysisGroup: "INFEKTIONSSEROLOGI",
		result: "Slutsvar",
		resultStatus: "Ovidimerat",
	},
	{
		date: "2025-02-13 14:05",
		orderedBy: "Eva Axelsson (läkare)",
		department: "Neurologisk avdelning Ösergötland",
		question: "Intratekala antikroppar?",
		analysisGroup: "SAMTEST",
		result: "Slutsvar",
		resultStatus: "Ovidimerat",
	},
	{
		date: "2025-02-13 14:05",
		orderedBy: "Eva Olsson (läkare)",
		department: "Ortopedisk avdelning Norrland",
		question: "",
		analysisGroup: "SÅR/SEKRET ODLING",
		result: "Slutsvar",
		resultStatus: "Ovidimerat",
	},
	{
		date: "2025-02-13 14:05",
		orderedBy: "Eva Olsson (läkare)",
		department: "Ortopedisk avdelning Norrland",
		question: "",
		analysisGroup: "SÅR/SEKRET ODLING",
		result: "Slutsvar",
		resultStatus: "Ovidimerat",
	},
	{
		date: "2025-02-13 14:05",
		orderedBy: "Eva Olsson (läkare)",
		department: "Ortopedisk avdelning Norrland",
		question: "",
		analysisGroup: "SÅR/SEKRET ODLING",
		result: "Slutsvar",
		resultStatus: "Ovidimerat",
	},
	{
		date: "2025-02-13 14:05",
		orderedBy: "Eva Olsson (läkare)",
		department: "Ortopedisk avdelning Norrland",
		question: "",
		analysisGroup: "SÅR/SEKRET ODLING",
		result: "Slutsvar",
		resultStatus: "Ovidimerat",
	},
	{
		date: "2025-02-13 14:05",
		orderedBy: "Eva Olsson (läkare)",
		department: "Ortopedisk avdelning Norrland",
		question: "",
		analysisGroup: "SÅR/SEKRET ODLING",
		result: "Slutsvar",
		resultStatus: "Ovidimerat",
	},
	{
		date: "2025-02-13 14:05",
		orderedBy: "Eva Olsson (läkare)",
		department: "Ortopedisk avdelning Norrland",
		question: "",
		analysisGroup: "SÅR/SEKRET ODLING",
		result: "Slutsvar",
		resultStatus: "Ovidimerat",
	},
];

export const Default: Story = {
	args: {
		patientInfo: samplePatientInfo,
		records: sampleRecords,
		totalRecords: 28,
		shownRecords: 10,
		onShowMore: () => console.log("Show more clicked"),
		onShowAll: () => console.log("Show all clicked"),
		onChangePatient: () => console.log("Change patient clicked"),
	},
};

export const FewRecords: Story = {
	args: {
		patientInfo: samplePatientInfo,
		records: sampleRecords.slice(0, 3),
		totalRecords: 28,
		shownRecords: 3,
		onShowMore: () => console.log("Show more clicked"),
		onShowAll: () => console.log("Show all clicked"),
		onChangePatient: () => console.log("Change patient clicked"),
	},
};

export const MobileView: Story = {
	args: {
		patientInfo: samplePatientInfo,
		records: sampleRecords.slice(0, 5),
		totalRecords: 28,
		shownRecords: 5,
		onShowMore: () => console.log("Show more clicked"),
		onShowAll: () => console.log("Show all clicked"),
		onChangePatient: () => console.log("Change patient clicked"),
	},
	parameters: {
		viewport: {
			defaultViewport: "mobile1",
		},
	},
};

export const DifferentPatient: Story = {
	args: {
		patientInfo: {
			id: "19850515-1234",
			name: "Erik Johansson",
			gender: "Man",
			age: 39,
		},
		records: [
			{
				date: "2025-01-15 10:30",
				orderedBy: "Anna Svensson (läkare)",
				department: "Kardiologi",
				question: "Hjärtenzym?",
				analysisGroup: "KLINISK KEMI",
				result: "Slutsvar",
				resultStatus: "Komplett",
			},
			{
				date: "2025-01-10 14:15",
				orderedBy: "Peter Lindqvist (läkare)",
				department: "Internmedicin",
				question: "Leverprover?",
				analysisGroup: "BIOCHEM",
				result: "Preliminärt",
				resultStatus: "Delvis",
			},
		],
		totalRecords: 15,
		shownRecords: 2,
		onShowMore: () => console.log("Show more clicked"),
		onShowAll: () => console.log("Show all clicked"),
		onChangePatient: () => console.log("Change patient clicked"),
	},
};
