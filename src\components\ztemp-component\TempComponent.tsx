import type { HTMLAttributes } from "react";

export interface TempComponentProps extends HTMLAttributes<HTMLDivElement> {
	/**
	 * Button contents
	 */
	children: React.ReactNode;
	/**
	 * Optional click handler
	 */
	onClick?: () => void;
	/**
	 * variant
	 */
	variant?: "primary" | "secondary";
}

/**
 * Primary UI component for user interaction
 */
export const TempComponent = ({
	children,
	variant = "primary",
	...props
}: TempComponentProps) => {
	const baseClasses = "px-4 rounded font-semibold";

	const variantClasses = {
		primary: "bg-blue-600 text-white hover:bg-blue-700",
		secondary: "bg-gray-600 text-white hover:bg-gray-700",
	};

	return (
		<div className={`${baseClasses} ${variantClasses[variant]}}`} {...props}>
			{children}
		</div>
	);
};

export default TempComponent;
