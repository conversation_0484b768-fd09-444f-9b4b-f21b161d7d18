import { render, screen } from "@testing-library/react";
import { describe, expect, it } from "vitest";
import { LabHeaderMenu } from "./LabHeaderMenu";

describe("LabHeaderMenu", () => {
	it("renders correctly with title and description", () => {
		const title = "Test Title";
		const description = "Test Description";
		render(<LabHeaderMenu title={title} description={description} />);

		expect(screen.getByText(title)).toBeInTheDocument();
		expect(screen.getByText(description)).toBeInTheDocument();
	});
});
