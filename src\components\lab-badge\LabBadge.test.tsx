import { fireEvent, render, screen } from "@testing-library/react";
import { describe, expect, it, vi } from "vitest";
import { LabBadge } from "./LabBadge";

describe("LabBadge", () => {
	it("renders badge text correctly", () => {
		render(<LabBadge>Test Badge</LabBadge>);
		expect(screen.getByText("Test Badge")).toBeInTheDocument();
	});

	it("renders without close button by default", () => {
		render(<LabBadge>Test Badge</LabBadge>);
		expect(screen.queryByRole("button")).not.toBeInTheDocument();
	});

	it("renders close button when dismissible is true", () => {
		render(<LabBadge dismissible>Test Badge</LabBadge>);
		expect(screen.getByRole("button")).toBeInTheDocument();
		expect(screen.getByLabelText("Remove badge")).toBeInTheDocument();
	});

	it("calls onDismiss when close button is clicked", () => {
		const handleDismiss = vi.fn();
		render(
			<LabBadge dismissible onDismiss={handleDismiss}>
				Test Badge
			</LabBadge>,
		);

		fireEvent.click(screen.getByRole("button"));
		expect(handleDismiss).toHaveBeenCalledTimes(1);
	});

	it("applies correct size classes", () => {
		const { rerender } = render(<LabBadge size="small">Small</LabBadge>);
		expect(screen.getByText("Small").parentElement).toHaveClass("h-7");

		rerender(<LabBadge size="medium">Medium</LabBadge>);
		expect(screen.getByText("Medium").parentElement).toHaveClass("h-9");

		rerender(<LabBadge size="large">Large</LabBadge>);
		expect(screen.getByText("Large").parentElement).toHaveClass("h-11");
	});

	it("applies correct variant classes", () => {
		const { rerender } = render(<LabBadge variant="default">Default</LabBadge>);
		expect(screen.getByText("Default").parentElement).toHaveClass(
			"bg-cp-grey--150",
		);

		rerender(<LabBadge variant="info">Info</LabBadge>);
		expect(screen.getByText("Info").parentElement).toHaveClass(
			"bg-cp-saphire--bright-alpha-12",
		);

		rerender(<LabBadge variant="success">Success</LabBadge>);
		expect(screen.getByText("Success").parentElement).toHaveClass(
			"bg-cp-1177-grass-background",
		);

		rerender(<LabBadge variant="warning">Warning</LabBadge>);
		expect(screen.getByText("Warning").parentElement).toHaveClass(
			"bg-cp-1177-sun-background",
		);

		rerender(<LabBadge variant="error">Error</LabBadge>);
		expect(screen.getByText("Error").parentElement).toHaveClass(
			"bg-cp-red--bright",
		);
	});

	it("applies custom className", () => {
		render(<LabBadge className="custom-class">Test</LabBadge>);
		expect(screen.getByText("Test").parentElement).toHaveClass("custom-class");
	});

	it("passes through HTML attributes", () => {
		render(<LabBadge data-testid="custom-badge">Test</LabBadge>);
		expect(screen.getByTestId("custom-badge")).toBeInTheDocument();
	});

	it("handles keyboard navigation on close button", () => {
		const handleDismiss = vi.fn();
		render(
			<LabBadge dismissible onDismiss={handleDismiss}>
				Test Badge
			</LabBadge>,
		);

		const closeButton = screen.getByRole("button");
		closeButton.focus();
		fireEvent.keyDown(closeButton, { key: "Enter" });
		// Note: Testing actual Enter key handling would require more complex setup
		// This just tests that the button can receive focus
		expect(closeButton).toHaveFocus();
	});
});
