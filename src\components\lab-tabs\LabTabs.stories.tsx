import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";
import React from "react";
import LabTabs from "./LabTabs";

const meta: Meta<typeof LabTabs> = {
	title: "UI/Lab Tabs",
	component: LabTabs,
	parameters: {
		layout: "centered",
	},
	tags: ["autodocs"],
	argTypes: {
		activeTab: {
			control: { type: "text" },
		},
	},
};

export default meta;
type Story = StoryObj<typeof LabTabs>;

const sampleTabs = [
	{ id: "tab1", label: "Tab 1" },
	{ id: "tab2", label: "Tab 2" },
];

export const Default: Story = {
	args: {
		tabs: sampleTabs,
		activeTab: "tab1",
		onTabChange: (tabId) => console.log("Tab changed to:", tabId),
	},
};

export const SecondTabActive: Story = {
	args: {
		tabs: sampleTabs,
		activeTab: "tab2",
		onTabChange: (tabId) => console.log("Tab changed to:", tabId),
	},
};

export const MultipleTabs: Story = {
	args: {
		tabs: [
			{ id: "overview", label: "Overview" },
			{ id: "details", label: "Details" },
			{ id: "settings", label: "Settings" },
			{ id: "advanced", label: "Advanced" },
		],
		activeTab: "details",
		onTabChange: (tabId) => console.log("Tab changed to:", tabId),
	},
};

export const Interactive: Story = {
	args: {
		tabs: sampleTabs,
		activeTab: "tab1",
	},
	render: (args) => {
		const [activeTab, setActiveTab] = React.useState(args.activeTab);

		return (
			<div className="w-96">
				<LabTabs {...args} activeTab={activeTab} onTabChange={setActiveTab} />
				<div className="mt-4 p-4 bg-ct-background border border-cp-grey--175 rounded">
					<p className="text-ct-foreground font-sans">
						Active tab content: <strong>{activeTab}</strong>
					</p>
				</div>
			</div>
		);
	},
};
