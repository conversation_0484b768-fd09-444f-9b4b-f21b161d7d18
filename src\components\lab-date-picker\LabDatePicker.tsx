import type { InputHTMLAttributes } from "react";
import { useRef, useState } from "react";
import "../../styles/public/tailwind.css";
import { RiCalendarCheckLine } from "@remixicon/react";

export interface LabDatePickerProps
	extends Omit<InputHTMLAttributes<HTMLInputElement>, "type" | "onChange"> {
	/**
	 * Selected date value (ISO string format: YYYY-MM-DD)
	 */
	value?: string;
	/**
	 * Callback fired when date is selected
	 */
	onChange?: (date: string) => void;
	/**
	 * Label for the date picker button
	 */
	label?: string;
	/**
	 * Minimum selectable date (ISO string format: YYYY-MM-DD)
	 */
	min?: string;
	/**
	 * Maximum selectable date (ISO string format: YYYY-MM-DD)
	 */
	max?: string;
	/**
	 * Additional CSS classes
	 */
	className?: string;
}

/**
 * Date picker component with calendar icon trigger
 */
export const LabDatePicker = ({
	value = "",
	onChange,
	label = "Välj datum",
	min,
	max,
	className = "",
	id,
	...props
}: LabDatePickerProps) => {
	const [isOpen, setIsOpen] = useState(false);
	const dateInputRef = useRef<HTMLInputElement>(null);

	const handleButtonClick = () => {
		if (!props.disabled) {
			setIsOpen(true);
			// Trigger the native date picker by focusing the hidden input
			setTimeout(() => {
				dateInputRef.current?.focus();
				dateInputRef.current?.showPicker?.();
			}, 0);
		}
	};

	const handleDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
		const newDate = event.target.value;
		onChange?.(newDate);
		setIsOpen(false);
	};

	const handleKeyDown = (event: React.KeyboardEvent<HTMLButtonElement>) => {
		if ((event.key === "Enter" || event.key === " ") && !props.disabled) {
			event.preventDefault();
			handleButtonClick();
		}
	};

	const formatDisplayDate = (dateStr: string) => {
		if (!dateStr) return label;

		try {
			const date = new Date(dateStr);
			return date.toLocaleDateString("sv-SE"); // Swedish format: YYYY-MM-DD
		} catch {
			return label;
		}
	};

	const inputId =
		id || `date-picker-${Math.random().toString(36).substr(2, 9)}`;

	return (
		<div className={`relative ${className}`}>
			{/* Visible button that matches the Figma design */}
			<button
				type="button"
				onClick={handleButtonClick}
				onKeyDown={handleKeyDown}
				disabled={props.disabled}
				className={`
					inline-flex h-12 px-6 py-3 justify-center items-center gap-4
					flex-shrink-0 rounded border-2 border-ct-border bg-ct-button-bg
					text-ct-foreground font-sans text-base font-bold leading-6
					transition-colors duration-200
					hover:bg-ct-button-bg--hover 
					active:bg-ct-button-bg--active
					focus:outline-none focus:ring-2 focus:ring-ct-focus focus:ring-offset-2
					disabled:opacity-50 disabled:cursor-not-allowed
				`}
				aria-label={
					value ? `Selected date: ${formatDisplayDate(value)}` : label
				}
				aria-expanded={isOpen}
				aria-haspopup="dialog"
			>
				<i className="ri-calendar-check-inline" />
				<RiCalendarCheckLine />
				<span>{formatDisplayDate(value)}</span>
			</button>

			{/* Hidden native date input */}
			<input
				ref={dateInputRef}
				type="date"
				id={inputId}
				value={value}
				onChange={handleDateChange}
				min={min}
				max={max}
				className="absolute opacity-0 pointer-events-none -z-10"
				tabIndex={-1}
				{...props}
			/>
		</div>
	);
};

export default LabDatePicker;
