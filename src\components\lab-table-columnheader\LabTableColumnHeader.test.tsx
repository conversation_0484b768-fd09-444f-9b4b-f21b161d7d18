import { render, screen } from "@testing-library/react";
import { describe, expect, it } from "vitest";
import { LabTableColumnHeader } from "./LabTableColumnHeader";

describe("LabTableColumnHeader", () => {
	it("renders cell content correctly", () => {
		render(<LabTableColumnHeader>Test Content</LabTableColumnHeader>);
		expect(screen.getByText("Test Content")).toBeInTheDocument();
	});

	it("renders as td by default", () => {
		render(<LabTableColumnHeader>Test Content</LabTableColumnHeader>);
		const cell = screen.getByText("Test Content").closest("td");
		expect(cell).toBeInTheDocument();
	});

	it("renders as th when type is th", () => {
		render(
			<LabTableColumnHeader type="th">Header Content</LabTableColumnHeader>,
		);
		const cell = screen.getByText("Header Content").closest("th");
		expect(cell).toBeInTheDocument();
	});

	it("applies correct alignment classes", () => {
		const { rerender } = render(
			<LabTableColumnHeader textAlign="left">Left</LabTableColumnHeader>,
		);
		expect(screen.getByText("Left").closest("td")).toHaveClass("items-start");

		rerender(
			<LabTableColumnHeader textAlign="center">Center</LabTableColumnHeader>,
		);
		expect(screen.getByText("Center").closest("td")).toHaveClass(
			"items-center",
		);

		rerender(
			<LabTableColumnHeader textAlign="right">Right</LabTableColumnHeader>,
		);
		expect(screen.getByText("Right").closest("td")).toHaveClass("items-end");
	});

	it("applies correct type-specific classes", () => {
		const { rerender } = render(
			<LabTableColumnHeader type="th">Header</LabTableColumnHeader>,
		);
		expect(screen.getByText("Header").closest("th")).toHaveClass("font-bold");
		expect(screen.getByText("Header").closest("th")).toHaveClass("border-t-2");

		rerender(<LabTableColumnHeader type="td">Data</LabTableColumnHeader>);
		expect(screen.getByText("Data").closest("td")).toHaveClass("font-normal");
		expect(screen.getByText("Data").closest("td")).not.toHaveClass(
			"border-t-2",
		);
	});

	it("applies custom className", () => {
		render(
			<LabTableColumnHeader className="custom-class">
				Test
			</LabTableColumnHeader>,
		);
		expect(screen.getByText("Test").closest("td")).toHaveClass("custom-class");
	});

	it("passes through HTML attributes", () => {
		render(
			<LabTableColumnHeader data-testid="custom-cell">
				Test
			</LabTableColumnHeader>,
		);
		expect(screen.getByTestId("custom-cell")).toBeInTheDocument();
	});

	it("has correct base styling classes", () => {
		render(<LabTableColumnHeader>Test</LabTableColumnHeader>);
		const cell = screen.getByText("Test").closest("td");
		expect(cell).toHaveClass("flex");
		expect(cell).toHaveClass("flex-col");
		expect(cell).toHaveClass("justify-center");
		expect(cell).toHaveClass("px-4");
		expect(cell).toHaveClass("py-3");
		expect(cell).toHaveClass("border-b");
		expect(cell).toHaveClass("border-ct-border");
		expect(cell).toHaveClass("text-ct-foreground");
	});

	it("renders complex content correctly", () => {
		render(
			<LabTableColumnHeader>
				<div>
					<span>Complex</span> Content
				</div>
			</LabTableColumnHeader>,
		);
		expect(screen.getByText("Complex")).toBeInTheDocument();
		expect(screen.getByText("Content")).toBeInTheDocument();
	});
});
