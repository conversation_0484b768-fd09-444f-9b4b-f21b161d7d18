# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Build outputs
dist/
coverage/
storybook-static/

# Version control
.git/
.gitignore

# Environment files
.env*
!.env.example

# OS files
.DS_Store
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Test files
junit.xml

# Build artifacts
*.tgz
*.tar.gz

# Docker files
Dockerfile*
.dockerignore

# Documentation
README.md
*.md
