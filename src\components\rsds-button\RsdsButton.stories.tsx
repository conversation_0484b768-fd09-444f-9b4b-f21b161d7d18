import { RiArrowDownSLine, RiFileLine } from "@remixicon/react";
import type { Meta, StoryObj } from "@storybook/react-vite";
import RsdsButton from "./RsdsButton";

const meta: Meta<typeof RsdsButton> = {
	title: "UI/Rsds Button",
	component: RsdsButton,
	parameters: {
		layout: "centered",
	},
	tags: ["autodocs"],
	argTypes: {
		variant: {
			control: { type: "select" },
			options: ["main", "default", "ghost"],
		},
	},
};

export default meta;
type Story = StoryObj<typeof RsdsButton>;

export const Main: Story = {
	args: {
		variant: "main",
		form: "rounded",
		children: (
			<div className="flex gap-x-1">
				<RiFileLine />
				<span>Label</span>
				<RiArrowDownSLine />
			</div>
		),
	},
};
export const Disabled: Story = {
	args: {
		variant: "main",
		form: "rounded",
		children: (
			<div className="flex gap-x-1">
				<RiFileLine />
				<span>Label</span>
				<RiArrowDownSLine />
			</div>
		),
		disabled: true,
	},
};

export const Default: Story = {
	args: {
		variant: "default",
		children: (
			<div className="flex gap-x-1">
				<RiFileLine />
				<span>Label</span>
				<RiArrowDownSLine />
			</div>
		),
	},
};

export const Ghost: Story = {
	args: {
		variant: "ghost",
		children: (
			<div className="flex gap-x-1">
				<RiFileLine />
				<span>Label</span>
				<RiArrowDownSLine />
			</div>
		),
	},
};

export const Square: Story = {
	args: {
		children: <div className="flex gap-x-1">
			<RiFileLine />
			<span>Label</span>
			<RiArrowDownSLine />
		</div>,
		form: "square",
	},
};
