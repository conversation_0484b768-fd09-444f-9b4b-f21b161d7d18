import type { <PERSON>a, StoryObj } from "@storybook/react-vite";
import React, { useId } from "react";
import LabDatePicker from "./LabDatePicker";

const meta: Meta<typeof LabDatePicker> = {
	title: "UI/Lab Date Picker",
	component: LabDatePicker,
	parameters: {
		layout: "centered",
	},
	tags: ["autodocs"],
	argTypes: {
		value: {
			control: { type: "text" },
		},
		label: {
			control: { type: "text" },
		},
		min: {
			control: { type: "text" },
		},
		max: {
			control: { type: "text" },
		},
	},
};

export default meta;
type Story = StoryObj<typeof LabDatePicker>;

export const Default: Story = {
	args: {
		label: "Välj datum",
		onChange: (date) => console.log("Selected date:", date),
	},
};

export const WithSelectedDate: Story = {
	args: {
		label: "Välj datum",
		value: "2024-01-15",
		onChange: (date) => console.log("Selected date:", date),
	},
};

export const CustomLabel: Story = {
	args: {
		label: "Choose date",
		onChange: (date) => console.log("Selected date:", date),
	},
};

export const WithMinMax: Story = {
	args: {
		label: "Välj datum",
		min: "2024-01-01",
		max: "2024-12-31",
		onChange: (date) => console.log("Selected date:", date),
	},
};

export const Interactive: Story = {
	args: {
		label: "Välj datum",
	},
	render: (args) => {
		const [selectedDate, setSelectedDate] = React.useState("");

		return (
			<div className="space-y-4">
				<LabDatePicker
					{...args}
					value={selectedDate}
					onChange={setSelectedDate}
				/>
				{selectedDate && (
					<div className="p-4 bg-ct-background border border-ct-border rounded">
						<p className="text-ct-foreground font-sans">
							<strong>Selected date:</strong> {selectedDate}
						</p>
						<p className="text-ct-foreground font-sans text-sm mt-1">
							Formatted: {new Date(selectedDate).toLocaleDateString("sv-SE")}
						</p>
					</div>
				)}
			</div>
		);
	},
};

export const Disabled: Story = {
	args: {
		label: "Välj datum",
		value: "2024-01-15",
		disabled: true,
		onChange: (date) => console.log("Selected date:", date),
	},
};

export const MultipleDatePickers: Story = {
	render: () => {
		const [startDate, setStartDate] = React.useState("");
		const [endDate, setEndDate] = React.useState("");
		const id = useId();
		const id2 = useId();

		return (
			<div className="flex gap-4">
				<div className="space-y-2">
					<label
						className="block text-ct-foreground font-sans text-sm font-bold"
						htmlFor={id}
						aria-label="label for startDate"
					>
						Start Date
					</label>
					<LabDatePicker
						id={id}
						label="Välj startdatum"
						value={startDate}
						onChange={setStartDate}
						max={endDate || undefined}
					/>
				</div>
				<div className="space-y-2">
					<label
						className="block text-ct-foreground font-sans text-sm font-bold"
						htmlFor={id2}
						aria-label="label for endpicker"
					>
						End Date
					</label>
					<LabDatePicker
						id={id2}
						label="Välj slutdatum"
						value={endDate}
						onChange={setEndDate}
						min={startDate || undefined}
					/>
				</div>
			</div>
		);
	},
};
