import type { InputHTMLAttributes } from "react";
import "../../styles/public/tailwind.css";
import { RiSearchLine } from "@remixicon/react";

export interface LabSearchInputProps
	extends Omit<InputHTMLAttributes<HTMLInputElement>, "type" | "onChange"> {
	/**
	 * Input value
	 */
	value?: string;
	/**
	 * Placeholder text
	 */
	placeholder?: string;
	/**
	 * Call<PERSON> fired when the input value changes
	 */
	onChange?: (value: string) => void;
	/**
	 * Callback fired when the search button is clicked
	 */
	onSearch?: (value: string) => void;
	/**
	 * Additional CSS classes
	 */
	className?: string;
}

/**
 * Search input component with integrated search button
 */
export const LabSearchInput = ({
	value = "",
	placeholder = "Sök...",
	onChange,
	onSearch,
	className = "",
	id,
	...props
}: LabSearchInputProps) => {
	const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
		onChange?.(event.target.value);
	};

	const handleSearchClick = () => {
		onSearch?.(value);
	};

	const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
		if (event.key === "Enter" && !props.disabled) {
			event.preventDefault();
			onSearch?.(value);
		}
	};

	const inputId =
		id || `search-input-${Math.random().toString(36).substr(2, 9)}`;

	return (
		<div
			className={`flex items-start gap-0 rounded border-2 border-ct-border bg-ct-background h-12 max-w-96 ${className}`}
		>
			<div className="flex h-12 px-3 items-center flex-1">
				<input
					type="text"
					id={inputId}
					value={value}
					placeholder={placeholder}
					onChange={handleInputChange}
					onKeyDown={handleKeyDown}
					className="w-full text-ct-foreground font-sans text-base font-normal leading-6 bg-transparent border-none outline-none placeholder:text-ct-foreground--subdued"
					{...props}
				/>
			</div>
			<button
				type="button"
				onClick={handleSearchClick}
				className="flex w-12 h-12 justify-center items-center flex-shrink-0 rounded bg-transparent hover:bg-ct-bg--hover active:bg-ct-bg--active transition-colors focus:outline-none focus:ring-2 focus:ring-ct-focus focus:ring-offset-2"
				aria-label="Sök"
			>
				<RiSearchLine />
			</button>
		</div>
	);
};

export default LabSearchInput;
