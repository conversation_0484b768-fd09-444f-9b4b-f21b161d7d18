import { fireEvent, render, screen } from "@testing-library/react";
import { describe, expect, test, vi } from "vitest";
import { type ButtonGroupOption, LabButtonGroup } from "./LabButtonGroup";

const defaultOptions: ButtonGroupOption[] = [
	{ id: "1", label: "1 år" },
	{ id: "3", label: "3 år" },
	{ id: "5", label: "5 år" },
	{ id: "max", label: "Max" },
];

describe("LabButtonGroup", () => {
	test("renders all options", () => {
		render(<LabButtonGroup options={defaultOptions} />);

		defaultOptions.forEach((option) => {
			expect(
				screen.getByRole("radio", { name: option.label }),
			).toBeInTheDocument();
		});
	});

	test("renders with selected option", () => {
		render(<LabButtonGroup options={defaultOptions} selectedId="3" />);

		const selectedButton = screen.getByRole("radio", { name: "3 år" });
		expect(selectedButton).toHaveAttribute("aria-checked", "true");

		const unselectedButton = screen.getByRole("radio", { name: "1 år" });
		expect(unselectedButton).toHaveAttribute("aria-checked", "false");
	});

	test("calls onChange when option is clicked", () => {
		const handleChange = vi.fn();
		render(
			<LabButtonGroup
				options={defaultOptions}
				selectedId="1"
				onChange={handleChange}
			/>,
		);

		fireEvent.click(screen.getByRole("radio", { name: "3 år" }));
		expect(handleChange).toHaveBeenCalledWith("3");
	});

	test("does not call onChange when disabled option is clicked", () => {
		const handleChange = vi.fn();
		const optionsWithDisabled = [
			...defaultOptions,
			{ id: "disabled", label: "Disabled", disabled: true },
		];

		render(
			<LabButtonGroup options={optionsWithDisabled} onChange={handleChange} />,
		);

		const disabledButton = screen.getByRole("radio", { name: "Disabled" });
		expect(disabledButton).toBeDisabled();

		fireEvent.click(disabledButton);
		expect(handleChange).not.toHaveBeenCalled();
	});

	test("applies custom className", () => {
		const { container } = render(
			<LabButtonGroup options={defaultOptions} className="custom-class" />,
		);

		expect(container.firstChild).toHaveClass("custom-class");
	});

	test("sets proper radiogroup role and aria-label", () => {
		render(<LabButtonGroup options={defaultOptions} name="test-group" />);

		const radioGroup = screen.getByRole("radiogroup");
		expect(radioGroup).toHaveAttribute("aria-label", "test-group");
	});

	test("first button has left border radius", () => {
		render(<LabButtonGroup options={defaultOptions} />);

		const firstButton = screen.getByRole("radio", { name: "1 år" });
		expect(firstButton).toHaveClass("rounded-l-sm");
	});

	test("last button has right border radius", () => {
		render(<LabButtonGroup options={defaultOptions} />);

		const lastButton = screen.getByRole("radio", { name: "Max" });
		expect(lastButton).toHaveClass("rounded-r-sm");
	});

	test("middle buttons have no border radius", () => {
		render(<LabButtonGroup options={defaultOptions} />);

		const middleButton = screen.getByRole("radio", { name: "3 år" });
		expect(middleButton).not.toHaveClass("rounded-l-sm");
		expect(middleButton).not.toHaveClass("rounded-r-sm");
	});

	test("handles empty options array", () => {
		const { container } = render(<LabButtonGroup options={[]} />);

		expect(container.firstChild).toBeInTheDocument();
		expect(screen.queryAllByRole("radio")).toHaveLength(0);
	});

	test("works with single option", () => {
		const singleOption = [{ id: "only", label: "Only Option" }];
		render(<LabButtonGroup options={singleOption} />);

		const button = screen.getByRole("radio", { name: "Only Option" });
		expect(button).toHaveClass("rounded-sm");
	});
});
