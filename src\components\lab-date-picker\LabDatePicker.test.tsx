/** biome-ignore-all lint/correctness/useUniqueElementIds: only used for testing */
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { describe, expect, it, vi } from "vitest";
import LabDatePicker from "./LabDatePicker";

// Mock showPicker for tests since it's not available in jsdom
Object.defineProperty(HTMLInputElement.prototype, "showPicker", {
	value: vi.fn(),
	writable: true,
});

describe("LabDatePicker", () => {
	it("renders with default label", () => {
		render(<LabDatePicker />);
		expect(screen.getByText("Välj datum")).toBeInTheDocument();
	});

	it("renders with custom label", () => {
		render(<LabDatePicker label="Choose date" />);
		expect(screen.getByText("Choose date")).toBeInTheDocument();
	});

	it("renders calendar icon", () => {
		render(<LabDatePicker />);
		const button = screen.getByRole("button");
		expect(button.querySelector("svg")).toBeInTheDocument();
	});

	it("displays selected date when value is provided", () => {
		render(<LabDatePicker value="2024-01-15" />);
		expect(screen.getByText("2024-01-15")).toBeInTheDocument();
	});

	it("calls onChange when date is selected", () => {
		const handleChange = vi.fn();
		render(<LabDatePicker onChange={handleChange} />);

		const hiddenInput = screen.getByDisplayValue("");
		fireEvent.change(hiddenInput, { target: { value: "2024-01-15" } });

		expect(handleChange).toHaveBeenCalledWith("2024-01-15");
	});

	it("focuses hidden input when button is clicked", async () => {
		const { container } = render(<LabDatePicker />);
		const button = screen.getByRole("button");
		const hiddenInput = container.querySelector(
			'input[type="date"]',
		) as HTMLInputElement;

		const focusSpy = vi.spyOn(hiddenInput, "focus");

		fireEvent.click(button);

		await waitFor(() => {
			expect(focusSpy).toHaveBeenCalled();
		});
	});

	it("handles Enter key press", async () => {
		const { container } = render(<LabDatePicker />);
		const button = screen.getByRole("button");
		const hiddenInput = container.querySelector(
			'input[type="date"]',
		) as HTMLInputElement;

		const focusSpy = vi.spyOn(hiddenInput, "focus");

		fireEvent.keyDown(button, { key: "Enter", code: "Enter" });

		await waitFor(() => {
			expect(focusSpy).toHaveBeenCalled();
		});
	});

	it("handles Space key press", async () => {
		const { container } = render(<LabDatePicker />);
		const button = screen.getByRole("button");
		const hiddenInput = container.querySelector(
			'input[type="date"]',
		) as HTMLInputElement;

		const focusSpy = vi.spyOn(hiddenInput, "focus");

		fireEvent.keyDown(button, { key: " ", code: "Space" });

		await waitFor(() => {
			expect(focusSpy).toHaveBeenCalled();
		});
	});

	it("does not respond to other key presses", () => {
		const { container } = render(<LabDatePicker />);
		const button = screen.getByRole("button");
		const hiddenInput = container.querySelector(
			'input[type="date"]',
		) as HTMLInputElement;

		const focusSpy = vi.spyOn(hiddenInput, "focus");

		fireEvent.keyDown(button, { key: "Tab", code: "Tab" });
		fireEvent.keyDown(button, { key: "Escape", code: "Escape" });

		expect(focusSpy).not.toHaveBeenCalled();
	});

	it("applies custom className", () => {
		render(<LabDatePicker className="custom-date-picker" />);
		const container = screen.getByRole("button").closest("div");
		expect(container).toHaveClass("custom-date-picker");
	});

	it("forwards props to hidden input", () => {
		render(
			<LabDatePicker
				data-testid="date-input"
				min="2024-01-01"
				max="2024-12-31"
			/>,
		);
		const hiddenInput = screen.getByTestId("date-input");
		expect(hiddenInput).toHaveAttribute("min", "2024-01-01");
		expect(hiddenInput).toHaveAttribute("max", "2024-12-31");
	});

	it("handles disabled state", () => {
		render(<LabDatePicker disabled />);
		const button = screen.getByRole("button");
		expect(button).toBeDisabled();
	});

	it("does not open picker when disabled and clicked", () => {
		const { container } = render(<LabDatePicker disabled />);
		const button = screen.getByRole("button");
		const hiddenInput = container.querySelector(
			'input[type="date"]',
		) as HTMLInputElement;

		const focusSpy = vi.spyOn(hiddenInput, "focus");

		fireEvent.click(button);

		expect(focusSpy).not.toHaveBeenCalled();
	});

	it("does not respond to keyboard when disabled", () => {
		const { container } = render(<LabDatePicker disabled />);
		const button = screen.getByRole("button");
		const hiddenInput = container.querySelector(
			'input[type="date"]',
		) as HTMLInputElement;

		const focusSpy = vi.spyOn(hiddenInput, "focus");

		fireEvent.keyDown(button, { key: "Enter", code: "Enter" });

		expect(focusSpy).not.toHaveBeenCalled();
	});

	it("generates unique id when not provided", () => {
		const { unmount: unmount1 } = render(<LabDatePicker />);
		const input1 = document.querySelector(
			'input[type="date"]',
		) as HTMLInputElement;
		const id1 = input1.id;
		unmount1();

		const { unmount: unmount2 } = render(<LabDatePicker />);
		const input2 = document.querySelector(
			'input[type="date"]',
		) as HTMLInputElement;
		const id2 = input2.id;
		unmount2();

		expect(id1).not.toBe(id2);
	});

	it("uses provided id", () => {
		render(<LabDatePicker id="custom-date-id" />);
		const hiddenInput = document.querySelector(
			'input[type="date"]',
		) as HTMLInputElement;
		expect(hiddenInput.id).toBe("custom-date-id");
	});

	it("formats date display correctly", () => {
		render(<LabDatePicker value="2024-01-15" />);
		expect(screen.getByText("2024-01-15")).toBeInTheDocument();
	});

	it("shows label when no date is selected", () => {
		render(<LabDatePicker label="Pick a date" />);
		expect(screen.getByText("Pick a date")).toBeInTheDocument();
	});

	it("has proper ARIA attributes", () => {
		render(<LabDatePicker value="2024-01-15" />);
		const button = screen.getByRole("button");

		expect(button).toHaveAttribute("aria-label");
		expect(button).toHaveAttribute("aria-expanded");
		expect(button).toHaveAttribute("aria-haspopup", "dialog");
	});

	it("updates button text when value changes", () => {
		const { rerender } = render(<LabDatePicker value="" label="Select date" />);
		expect(screen.getByText("Select date")).toBeInTheDocument();

		rerender(<LabDatePicker value="2024-01-15" label="Select date" />);
		expect(screen.getByText("2024-01-15")).toBeInTheDocument();
	});
});
