import type { HTMLAttributes, ReactNode } from "react";
import "../../styles/public/tailwind.css";

export interface LabTableCellProps
	extends Omit<HTMLAttributes<HTMLTableCellElement>, "children"> {
	/**
	 * Cell content
	 */
	children: ReactNode;
	/**
	 * Type of table cell
	 */
	type?: "th" | "td";
	/**
	 * Text alignment within the cell
	 */
	textAlign?: "left" | "center" | "right";

	/**
	 * Variant for different cell states
	 */
	variant?: "normal" | "elevated";
	/**
	 * Additional CSS classes
	 */
	className?: string;
}

/**
 * Table cell component for displaying data with normal and elevated variants
 */
export const LabTableCell = ({
	children,
	type = "td",
	textAlign = "left",
	variant = "normal",
	className = "",
	...props
}: LabTableCellProps) => {
	const Component = type;

	const baseClasses =
		"flex w-36 px-4 py-3 flex-col h-12 border-b text-base leading-6";

	const borderClasses = {
		normal: "border-b-ct-foreground",
		elevated: "border-b-cp-grey--700",
	};

	const textClasses = {
		normal: "text-ct-foreground font-normal",
		elevated: "text-cp-red font-bold text-cp-grey--700",
	};

	const alignmentClasses = {
		left: "items-start text-left",
		center: "items-center text-center",
		right: "items-end text-right",
	};

	return (
		<Component
			className={`
				${baseClasses}
				${borderClasses[variant]}
				${alignmentClasses[textAlign]}
				${textClasses[variant]}
				${className}
			`}
			{...props}
		>
			<div className="self-stretch">
				<span>{children}</span>
			</div>
		</Component>
	);
};

export default LabTableCell;
