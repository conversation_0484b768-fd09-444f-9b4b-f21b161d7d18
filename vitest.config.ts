import react from "@vitejs/plugin-react";
import { defineConfig } from "vitest/config";

export default defineConfig({
	plugins: [react()],
	test: {
		environment: "jsdom",
		globals: true,
		setupFiles: ["./vitest.setup.ts"],
		reporters: ["default", "junit"],
		outputFile: {
			junit: "./junit.xml",
		},
		coverage: {
			provider: "v8",
			reporter: ["text", "json", "html", "cobertura"],
			reportsDirectory: "./coverage",
			exclude: [
				// Default excludes
				"node_modules/**",
				"dist/**",
				"coverage/**",
				"storybook-static/**",
				"**/*.test.{ts,tsx,js,jsx}",
				"**/*.spec.{ts,tsx,js,jsx}",
				// Exclude Storybook story files
				"**/*.stories.*",
				// Exclude configuration files
				"*.config.{js,ts}",
				"**/*.config.{js,ts}",
				// Exclude Storybook configuration
				".storybook/**",
				// Exclude type definition files
				"**/*.d.ts",
				// Exclude index files (barrel exports)
				"**/index.ts",
			],
		},
	},
});
