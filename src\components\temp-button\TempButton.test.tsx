import { fireEvent, render, screen } from "@testing-library/react";
import { describe, expect, it, vi } from "vitest";
import { TempButton } from "./TempButton";

describe("Button", () => {
	it("renders correctly", () => {
		render(<TempButton>Click me</TempButton>);

		expect<HTMLElement>(screen.getByText("Click me")).toBeInTheDocument();
	});

	it("calls onClick when clicked", () => {
		const handleClick = vi.fn();
		render(<TempButton onClick={handleClick}>Click me</TempButton>);
		fireEvent.click(screen.getByText("Click me"));
		expect(handleClick).toHaveBeenCalledTimes(1);
	});

	it("does not call onClick when disabled", () => {
		const handleClick = vi.fn();
		render(
			<TempButton onClick={handleClick} disabled>
				Click me
			</TempButton>,
		);
		fireEvent.click(screen.getByText("Click me"));
		expect(handleClick).not.toHaveBeenCalled();
	});

	it("applies the correct variant class", () => {
		const { rerender } = render(
			<TempButton variant="primary">Primary</TempButton>,
		);
		expect<HTMLButtonElement>(screen.getByText("Primary")).toHaveClass(
			"bg-blue-600",
		);

		rerender(<TempButton variant="secondary">Secondary</TempButton>);
		expect(screen.getByText("Secondary")).toHaveClass("bg-gray-600");

		rerender(<TempButton variant="outline">Outline</TempButton>);
		expect(screen.getByText("Outline")).toHaveClass("border-gray-300");
	});

	it("applies the correct size class", () => {
		const { rerender } = render(<TempButton size="small">Small</TempButton>);
		expect(screen.getByText("Small")).toHaveClass("py-1");

		rerender(<TempButton size="medium">Medium</TempButton>);
		expect(screen.getByText("Medium")).toHaveClass("py-2");

		rerender(<TempButton size="large">Large</TempButton>);
		expect(screen.getByText("Large")).toHaveClass("py-3");
	});
});
