import { fireEvent, render, screen } from "@testing-library/react";
import { describe, expect, it, vi } from "vitest";
import type { LabHeaderMenuProps } from "../../components/lab-header-menu/LabHeaderMenu";
import type { LabTabsProps, TabItem } from "../../components/lab-tabs/LabTabs";
import type { RsdsButtonProps } from "../../components/rsds-button/RsdsButton";
import { ShowVe } from "./ShowVe";

// Mock the child components
vi.mock("../../components/lab-header-menu/LabHeaderMenu", () => ({
	default: ({ title, description, ...props }: LabHeaderMenuProps) => (
		<div data-testid="lab-header-menu" {...props}>
			<div data-testid="header-title">{title}</div>
			<div data-testid="header-description">{description}</div>
		</div>
	),
}));

vi.mock("../../components/lab-tabs/LabTabs", () => ({
	default: ({ tabs, activeTab, onTabChange, ...props }: LabTabsProps) => (
		<div data-testid="lab-tabs" {...props}>
			{tabs.map((tab: TabItem) => (
				<button
					type="button"
					key={tab.id}
					data-testid={`tab-${tab.id}`}
					onClick={() => onTabChange(tab.id)}
					className={activeTab === tab.id ? "active" : ""}
				>
					{tab.label}
				</button>
			))}
		</div>
	),
}));

vi.mock("../../components/rsds-button/RsdsButton", () => ({
	default: ({ children, variant, form, ...props }: RsdsButtonProps) => (
		<button
			data-testid="rsds-button"
			data-variant={variant}
			data-form={form}
			{...props}
		>
			{children}
		</button>
	),
}));

vi.mock("../search-patient/SearchPatient", () => ({
	default: () => (
		<div data-testid="search-patient-component">SearchPatient</div>
	),
}));

// Mock the Remix icon
vi.mock("@remixicon/react", () => ({
	RiErrorWarningLine: ({
		className,
		...props
	}: React.SVGAttributes<SVGSVGElement>) => (
		<svg data-testid="warning-icon" className={className} {...props} />
	),
}));

describe("ShowVe", () => {
	it("renders correctly with all main elements", () => {
		render(<ShowVe />);

		// Check for header menu
		expect(screen.getByTestId("lab-header-menu")).toBeInTheDocument();
		expect(screen.getByTestId("header-title")).toHaveTextContent(
			"Labsvar i Skåne",
		);
		expect(screen.getByTestId("header-description")).toHaveTextContent(
			"Du är inloggad som Sarah Wingaard, VoB Internmedicinsk vård Ystad",
		);

		// Check for tabs
		expect(screen.getByTestId("lab-tabs")).toBeInTheDocument();
		expect(screen.getByTestId("tab-show-ve")).toBeInTheDocument();
		expect(screen.getByTestId("tab-search-patient")).toBeInTheDocument();
	});

	it("renders patient information in navigation", () => {
		render(<ShowVe />);

		expect(
			screen.getByText("Patient: 19930303-2388 Sofie Lindberg (Kvinna, 32 år)"),
		).toBeInTheDocument();
	});

	it("renders warning message with icon", () => {
		render(<ShowVe />);

		expect(screen.getByTestId("warning-icon")).toBeInTheDocument();
		expect(
			screen.getByText("Alla tillgängliga uppgifter visas inte."),
		).toBeInTheDocument();

		// Check that the icon has the correct CSS class
		const warningIcon = screen.getByTestId("warning-icon");
		expect(warningIcon).toHaveClass("fill-cp-red");
	});

	it("displays show-ve tab content by default", () => {
		render(<ShowVe />);

		// Check for vårdgivare content
		expect(
			screen.getByText("Din vårdgivare Region Skåne (3)"),
		).toBeInTheDocument();

		// Check that SearchPatient component is not rendered initially
		expect(
			screen.queryByTestId("search-patient-component"),
		).not.toBeInTheDocument();
	});

	it("renders vårdenheter content correctly", () => {
		render(<ShowVe />);

		// Check for table content (the actual content in the component)
		expect(screen.getByText("Song")).toBeInTheDocument();
		expect(screen.getByText("Artist")).toBeInTheDocument();
		expect(screen.getByText("Year")).toBeInTheDocument();
		expect(
			screen.getByText("The Sliding Mr. Bones (Next Stop, Pottersville)"),
		).toBeInTheDocument();
	});

	it("renders emergency opening section", () => {
		render(<ShowVe />);

		expect(screen.getByText("Nödöppning")).toBeInTheDocument();
		expect(
			screen.getByText(
				"Du kan nödöppna patientens journal om det är en nödsituation och patienten inte kan ge samtycke.",
			),
		).toBeInTheDocument();
		expect(screen.getByText("Du kan se:")).toBeInTheDocument();
		expect(
			screen.getByText("Sammanhållen vårddokumentation"),
		).toBeInTheDocument();
		expect(
			screen.getByText("Spärrade enheter inom Region Skåne"),
		).toBeInTheDocument();
		expect(screen.getByText("All läsning loggas.")).toBeInTheDocument();
	});

	it("renders emergency button with correct props", () => {
		render(<ShowVe />);

		const emergencyButton = screen.getByTestId("rsds-button");
		expect(emergencyButton).toHaveTextContent("Nödöppna");
		expect(emergencyButton).toHaveAttribute("data-variant", "default");
		expect(emergencyButton).toHaveAttribute("data-form", "square");
	});

	it("switches to search patient tab when clicked", () => {
		render(<ShowVe />);

		// Initially, show-ve content should be visible
		expect(
			screen.getByText("Din vårdgivare Region Skåne (3)"),
		).toBeInTheDocument();
		expect(
			screen.queryByTestId("search-patient-component"),
		).not.toBeInTheDocument();

		// Click on search patient tab
		const searchPatientTab = screen.getByTestId("tab-search-patient");
		fireEvent.click(searchPatientTab);

		// Now search patient component should be visible
		expect(screen.getByTestId("search-patient-component")).toBeInTheDocument();
		// And show-ve content should not be visible
		expect(
			screen.queryByText("Din vårdgivare Region Skåne (3)"),
		).not.toBeInTheDocument();
	});

	it("switches back to show-ve tab when clicked", () => {
		render(<ShowVe />);

		// Click on search patient tab first
		const searchPatientTab = screen.getByTestId("tab-search-patient");
		fireEvent.click(searchPatientTab);

		// Verify search patient is shown
		expect(screen.getByTestId("search-patient-component")).toBeInTheDocument();

		// Click back on show-ve tab
		const showVeTab = screen.getByTestId("tab-show-ve");
		fireEvent.click(showVeTab);

		// Verify show-ve content is back
		expect(
			screen.getByText("Din vårdgivare Region Skåne (3)"),
		).toBeInTheDocument();
		expect(
			screen.queryByTestId("search-patient-component"),
		).not.toBeInTheDocument();
	});

	it("has proper CSS structure and classes", () => {
		render(<ShowVe />);

		// Check main container
		const mainContainer = screen.getByTestId("lab-header-menu").parentElement;
		expect(mainContainer).toHaveClass("w-full");

		// Check navigation structure
		const nav = screen.getByRole("navigation");
		expect(nav).toHaveClass("flex", "px-10", "justify-between");

		// Check main content area
		const main = screen.getByRole("main");
		expect(main).toHaveClass(
			"pl-[142px]",
			"border-b",
			"p-9",
			"flex",
			"flex-col",
			"gap-y-3",
		);
	});

	it("renders tabs with correct initial state", () => {
		render(<ShowVe />);

		const showVeTab = screen.getByTestId("tab-show-ve");
		const searchPatientTab = screen.getByTestId("tab-search-patient");

		expect(showVeTab).toHaveTextContent("Visa vårdenheter");
		expect(searchPatientTab).toHaveTextContent("Sök patient");

		// Check initial active state
		expect(showVeTab).toHaveClass("active");
		expect(searchPatientTab).not.toHaveClass("active");
	});
});
