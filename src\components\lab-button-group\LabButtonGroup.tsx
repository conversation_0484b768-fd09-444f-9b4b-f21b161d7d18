import "../../styles/public/tailwind.css";

export interface ButtonGroupOption {
	/**
	 * Unique identifier for the option
	 */
	id: string;
	/**
	 * Option label text
	 */
	label: string;
	/**
	 * Whether this option is disabled
	 */
	disabled?: boolean;
}

export interface LabButtonGroupProps {
	/**
	 * Array of button options
	 */
	options: ButtonGroupOption[];
	/**
	 * Currently selected option ID
	 */
	selectedId?: string;
	/**
	 * Callback fired when an option is clicked
	 */
	onChange?: (selectedId: string) => void;
	/**
	 * Additional CSS classes for the container
	 */
	className?: string;
	/**
	 * Button group name for accessibility
	 */
	name?: string;
}

/**
 * Button group component for selecting from multiple options
 */
export const LabButtonGroup = ({
	options,
	selectedId,
	onChange,
	className = "",
	name = "button-group",
}: LabButtonGroupProps) => {
	const handleOptionClick = (optionId: string) => {
		if (onChange) {
			onChange(optionId);
		}
	};

	return (
		<div
			className={`inline-flex items-center ${className}`}
			role="radiogroup"
			aria-label={name}
		>
			{options.map((option, index) => {
				const isSelected = option.id === selectedId;
				const isFirst = index === 0;
				const isLast = index === options.length - 1;

				// Determine border radius classes
				const borderRadiusClass =
					options.length === 1
						? "rounded-sm"
						: isFirst
							? "rounded-l-sm"
							: isLast
								? "rounded-r-sm"
								: "";

				return (
					// TODO look into
					// biome-ignore lint/a11y/useSemanticElements: Need to look into after demo
					<button
						key={option.id}
						type="button"
						onClick={() => handleOptionClick(option.id)}
						disabled={option.disabled}
						role="radio"
						aria-checked={isSelected}
						className={`
							relative
							flex
							h-12
							px-6
							py-3
							justify-center
							items-center
							gap-4
							border-2
							border-ct-border
							bg-ct-button-bg
							text-ct-foreground
							font-sans
							text-base
							font-bold
							leading-6
							transition-colors
							duration-200
							hover:bg-ct-button-bg--hover
							active:bg-ct-button-bg--active
							disabled:opacity-50
							disabled:cursor-not-allowed
							focus:outline-none
							focus:ring-2
							focus:ring-ct-focus
							focus:ring-offset-2
							${borderRadiusClass}
							${index > 0 ? "-ml-0.5" : ""}
							${isSelected ? "bg-ct-button-bg--selected z-10" : ""}
						`}
					>
						{isSelected && (
							<div
								className="absolute inset-0 rounded-sm bg-cp-saphire--bright-alpha-33 pointer-events-none"
								aria-hidden="true"
							/>
						)}
						<span className="relative z-10">{option.label}</span>
					</button>
				);
			})}
		</div>
	);
};

export default LabButtonGroup;
