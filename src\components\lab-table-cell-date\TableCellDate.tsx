import type { HTMLAttributes } from "react";
import "../../styles/public/tailwind.css";

export interface TableCellDateProps
	extends Omit<HTMLAttributes<HTMLTableCellElement>, "children"> {
	/**
	 * Date and time string in format "YYYY-MM-DD HH:mm"
	 */
	dateTime: string;
	/**
	 * Type of table cell
	 */
	type?: "th" | "td";
	/**
	 * Text alignment within the cell
	 */
	textAlign?: "left" | "center" | "right";

	/**
	 * Additional CSS classes
	 */
	className?: string;
}

/**
 * Table cell component specifically designed for displaying date and time
 */
export const TableCellDate = ({
	dateTime,
	type = "th",
	textAlign = "left",
	className = "",
	...props
}: TableCellDateProps) => {
	const Component = type;

	// Split the datetime string into date and time parts
	const [date, time] = dateTime.split(" ");

	const baseClasses = "flex flex-col justify-center px-4 py-3 w-35 h-18";

	const typeClasses = {
		th: "border-t-2 border-t-ct-foreground border-b border-ct-foreground",
		td: "border-b border-ct-foreground",
	};

	const alignmentClasses = {
		left: "items-start",
		center: "items-center",
		right: "items-end",
	};

	return (
		<Component
			className={`
				${baseClasses} 
				${typeClasses[type]} 
				${className}
			`
				.trim()
				.replace(/\s+/g, " ")}
			{...props}
		>
			<div
				className={`flex-1 flex flex-col text-ct-foreground font-sans text-base leading-6 ${alignmentClasses[textAlign]} `}
			>
				<span className="font-bold">{date} </span>
				<span className="font-normal">{time}</span>
			</div>
		</Component>
	);
};

export default TableCellDate;
