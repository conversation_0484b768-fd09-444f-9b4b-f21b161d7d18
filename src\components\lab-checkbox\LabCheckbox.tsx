import type { InputHTMLAttributes } from "react";
import "../../styles/public/tailwind.css";
import { Ri<PERSON><PERSON>ckFill } from "@remixicon/react";

export interface LabCheckboxProps
	extends Omit<InputHTMLAttributes<HTMLInputElement>, "type" | "onChange"> {
	/**
	 * Checkbox label text
	 */
	label: string;
	/**
	 * Optional description text
	 */
	description?: string;
	/**
	 * Whether the checkbox is checked
	 */
	checked?: boolean;
	/**
	 * Callback fired when the checkbox is toggled
	 */
	onChange?: (checked: boolean) => void;
	/**
	 * Additional CSS classes
	 */
	className?: string;
}

/**
 * Checkbox component for form inputs with optional description
 */
export const LabCheckbox = ({
	label,
	description,
	checked = false,
	onChange,
	className = "",
	id,
	...props
}: LabCheckboxProps) => {
	const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
		onChange?.(event.target.checked);
	};

	const checkboxId =
		id || `checkbox-${Math.random().toString(36).substr(2, 9)}`;

	return (
		<div className={`flex items-start gap-3 py-3 ${className}`}>
			<div className="relative flex-shrink-0">
				<input
					type="checkbox"
					id={checkboxId}
					checked={checked}
					onChange={handleChange}
					className="sr-only"
					{...props}
				/>
				<label
					htmlFor={checkboxId}
					className="flex w-6 h-6 justify-center items-center rounded border-2 border-ct-foreground bg-ct-background cursor-pointer transition-colors hover:bg-ct-bg--hover focus-within:ring-2 focus-within:ring-ct-focus focus-within:ring-offset-2"
				>
					{checked && <RiCheckFill />}
				</label>
			</div>
			<div className="flex flex-col justify-center flex-1">
				<label
					htmlFor={checkboxId}
					className={`text-ct-foreground font-sans text-base leading-6 cursor-pointer ${
						description ? "font-bold" : "font-normal"
					}`}
				>
					{label}
				</label>
				{description && (
					<p className="text-ct-foreground font-sans text-base font-normal leading-6 mt-0">
						{description}
					</p>
				)}
			</div>
		</div>
	);
};

export default LabCheckbox;
