name: rs-ui-react-components CI Pipeline

# This triggers the pipeline on pushes to main (like after a merge)
trigger:
  branches:
    include:
      - main


variables:
  nodeVersion: '22.x'
  tag: '$(Build.BuildId)'
  pnpm_config_cache: $(Pipeline.Workspace)/.pnpm-store

  # Docker image configuration
  dockerRegistryServiceConnection: 'sc-crlabdata-test'
  imageRepository: 'rs-ui-react-components-storybook'
  dockerfilePath: '$(Build.SourcesDirectory)/Dockerfile'
  

stages:
  - stage: Build
    displayName: Build and Test
    jobs:
      - job: BuildAndTest
        displayName: Build, Test
        timeoutInMinutes: 10

        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - template: templates/setup.yaml
            parameters:
              nodeVersion: $(nodeVersion)
              pnpm_config_cache: $(pnpm_config_cache)

          - template: templates/test.yaml
          
          - template: templates/build.yaml

          - template: templates/publish.yaml
            parameters:
              artifactName: 'rs-ui-react-components'

  - stage: StorybookStage
    displayName: Build Storybook
    dependsOn: Build
    jobs:
      - job: BuildStorybook
        displayName: Build Storybook Docker Image
        timeoutInMinutes: 15
        pool:
          vmImage: 'ubuntu-latest'
        
        steps:
          # Build Docker image
          - task: Docker@2
            displayName: 'Build Storybook Docker image'
            inputs:
              command: 'buildAndPush'
              repository: $(imageRepository)
              dockerfile: $(dockerfilePath)
              containerRegistry: $(dockerRegistryServiceConnection)
              tags: |
                $(tag)
                latest
              arguments: '--no-cache'