import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";
import React from "react";
import LabCheckbox from "./LabCheckbox";

const meta: Meta<typeof LabCheckbox> = {
	title: "UI/Lab Checkbox",
	component: LabCheckbox,
	parameters: {
		layout: "centered",
	},
	tags: ["autodocs"],
	argTypes: {
		checked: {
			control: { type: "boolean" },
		},
		label: {
			control: { type: "text" },
		},
		description: {
			control: { type: "text" },
		},
	},
};

export default meta;
type Story = StoryObj<typeof LabCheckbox>;

export const Unchecked: Story = {
	args: {
		label: "Label",
		checked: false,
	},
};

export const Checked: Story = {
	args: {
		label: "Label",
		checked: true,
	},
};

export const WithDescription: Story = {
	args: {
		label: "Label",
		description: "Description",
		checked: false,
	},
};

export const WithDescriptionChecked: Story = {
	args: {
		label: "Label",
		description: "Description",
		checked: true,
	},
};

export const Interactive: Story = {
	args: {
		label: "Interactive Checkbox",
		description: "Click to toggle the checkbox state",
	},
	render: (args) => {
		const [checked, setChecked] = React.useState(args.checked || false);

		return <LabCheckbox {...args} checked={checked} onChange={setChecked} />;
	},
};
